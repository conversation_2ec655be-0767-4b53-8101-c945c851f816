<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="?attr/chat_input_area_bg"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/input_title_line_color" />

    <com.tencent.qcloud.tuikit.tuichat.ui.view.input.ReplyPreviewBar
        android:id="@+id/reply_preview_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="5dp"
        android:visibility="gone"
        tools:visibility="visible"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="5dp"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/voice_input_switch"
            android:layout_width="@dimen/chat_input_icon_size"
            android:layout_height="@dimen/chat_input_icon_size"
            android:layout_margin="5dp"
            android:scaleType="fitXY"
            android:src="@drawable/action_audio_selector" />

        <com.tencent.qcloud.tuikit.tuichat.ui.view.input.TIMMentionEditText
            android:id="@+id/chat_message_input"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="5dp"
            android:layout_weight="1"
            android:background="@drawable/msg_editor_border"
            android:textSize="@dimen/chat_input_text_size"
            android:maxHeight="120dp"
            android:minHeight="@dimen/chat_input_editor_height"
            android:textCursorDrawable="@drawable/my_cursor"
            android:padding="5dp" />

        <Button
            android:id="@+id/chat_voice_input"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="5dp"
            android:layout_weight="1"
            android:background="@drawable/voice_btn_selector"
            android:text="@string/hold_say"
            android:textAllCaps="false"
            android:textColor="@color/text_color_gray"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/face_btn"
            android:layout_width="@dimen/chat_input_icon_size"
            android:layout_height="@dimen/chat_input_icon_size"
            android:layout_margin="5dp"
            android:scaleType="fitXY"
            android:src="@drawable/action_face_selector" />

        <ImageView
            android:id="@+id/more_btn"
            android:layout_width="@dimen/chat_input_icon_size"
            android:layout_height="@dimen/chat_input_icon_size"
            android:layout_margin="5dp"
            android:scaleType="fitXY"
            android:src="@drawable/action_more_selector" />

        <Button
            android:id="@+id/send_btn"
            android:layout_width="40dp"
            android:layout_height="@dimen/chat_input_icon_size"
            android:background="@drawable/message_send_border"
            android:text="@string/send"
            android:textColor="#fff"
            android:textSize="13sp"
            android:visibility="gone" />

    </LinearLayout>

    <com.tencent.qcloud.tuikit.tuichat.ui.view.input.ReplyPreviewBar
        android:id="@+id/quote_preview_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:paddingBottom="9.6dp"
        tools:visibility="visible"/>

    <RelativeLayout
        android:id="@+id/more_groups"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone" />

</LinearLayout>
