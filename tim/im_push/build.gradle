plugins {
    id 'com.android.library'
}

ext {
    // 是否要编译 uniapp 推送原生插件，默认为 false
    // Whether to compile uniapp and push native plugins，default is false
    UNIAPP_MODEL = false
}

android {
    compileSdkVersion rootProject.compileSdkVersion
    buildToolsVersion rootProject.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    sourceSets {
        main {
            if (!UNIAPP_MODEL) {
                manifest.srcFile 'src/main/AndroidManifest.xml'
                java.srcDirs = [
                        'src/main/java',
                        'src/main/native/java'
                ]
            } else {
                manifest.srcFile 'src/main/uniapp/AndroidManifest.xml'
                java.srcDirs = [
                        'src/main/java',
                        'src/main/uniapp/java'
                ]
            }
        }
    }
    namespace 'com.tencent.qcloud.tim.tuiofflinepush'
}

dependencies {
    implementation 'com.google.code.gson:gson:2.10.1'

    // Google FCM
    implementation "com.tencent.tpns:fcm:1.3.3.3-release"
    // google 云消息传递
    implementation('com.google.firebase:firebase-messaging:23.1.0')
    implementation "com.google.firebase:firebase-iid:21.1.0"
    // 小米
    implementation "com.tencent.tpns:xiaomi:1.3.3.3-release"
    // 魅族
    implementation "com.tencent.tpns:meizu:1.3.3.3-release"
    // OPPO
    implementation "com.tencent.tpns:oppo:1.3.3.3-release"
    implementation 'commons-codec:commons-codec:1.15'
    // vivo
    implementation "com.tencent.tpns:vivo:1.3.3.3-release"
    // 华为
    implementation 'com.tencent.tpns:huawei:1.3.3.3-release'
    implementation 'com.huawei.hms:push:6.12.0.300'

    if (!UNIAPP_MODEL) {
        if (isLibTimCoreSource.toBoolean()) {
            implementation project(':tim:imui_core')
        } else {
            implementation 'com.totwoo.tim:imui-core:1.0.0'
        }
    } else {
        implementation 'com.alibaba:fastjson:1.1.72.android'
        compileOnly fileTree(dir: 'libs', include: ['uniapp-v8-release.aar'])
    }
}