{"name": "TencentCloud-TUIOfflinePush", "id": "TencentCloud-TUIOfflinePush", "version": "1.0.0", "description": "uni 离线推送插件", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "module", "name": "TencentCloud-TUIOfflinePush", "class": "com.tencent.qcloud.tim.tuiofflinepush.TUIOfflinePushModule"}], "hooksClass": "com.tencent.qcloud.tim.tuiofflinepush.TUIOfflinePushAppProxy", "integrateType": "aar", "dependencies": ["com.tencent.tpns:fcm:*******-release", "com.google.firebase:firebase-messaging:19.0.1", "com.tencent.tpns:xiaomi:*******-release", "com.tencent.tpns:meizu:*******-release", "com.tencent.tpns:oppo:*******-release", "commons-codec:commons-codec:1.15", "com.tencent.tpns:vivo:*******-release", "com.tencent.tpns:hua<PERSON>:*******-release", "com.huawei.hms:push:6.12.0.300"], "compileOptions": {"sourceCompatibility": "1.8", "targetCompatibility": "1.8"}, "minSdkVersion": "21", "parameters": {"com.vivo.push.app_id": {"des": "VIVO推送 app_id", "placeholder": "VIVO_APPID"}, "com.vivo.push.api_key": {"des": "VIVO推送 api_key", "placeholder": "VIVO_APPKEY"}}}, "ios": {}}}