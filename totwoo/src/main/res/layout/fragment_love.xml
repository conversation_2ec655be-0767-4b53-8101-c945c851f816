<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/love_fragment_content_cl"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <ImageView
        android:id="@+id/fragment_love_bg_iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/new_heart_pair_bg" />

    <!--    已配对情况下, 正常状态整体布局-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/love_pair_content_cl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:visibility="visible">

        <!--    背景图底部白色波纹蒙版-->
        <!--        <ImageView-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:scaleType="centerCrop"-->
        <!--            android:src="@drawable/love_pair_background_wave"-->
        <!--            app:layout_constraintBottom_toBottomOf="parent" />-->

        <!--    顶部黑色蒙版 -->
        <!--        <ImageView-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="@dimen/home_title_margin_height"-->
        <!--            android:scaleType="fitXY"-->
        <!--            android:src="@drawable/love_fragment_bg_mask"-->
        <!--            android:visibility="gone"-->
        <!--            app:layout_constraintTop_toTopOf="parent" />-->

        <!--        配对状态布局-->
        <RelativeLayout
            android:layout_width="138dp"
            android:layout_height="91dp"
            android:layout_marginStart="@dimen/common_padding_parent_middle"
            android:layout_marginTop="180dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/love_fragment_info_iv"
                android:layout_width="138dp"
                android:layout_height="68dp"
                android:layout_marginTop="23dp"
                android:src="@drawable/shape_love_couple_info_bg_new" />

            <RelativeLayout
                android:id="@+id/love_fragment_head_rl"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true">


                <com.totwoo.totwoo.widget.RoundImageView
                    android:id="@+id/love_other_iv"
                    android:layout_width="@dimen/love_head_icon_size"
                    android:layout_height="@dimen/love_head_icon_size"
                    android:scaleType="centerCrop"
                    app:border_color="#f5f5f5"
                    app:border_width="3dp"
                    app:type="circle"
                    tools:src="@drawable/default_head_yellow" />

                <com.totwoo.totwoo.widget.RoundImageView
                    android:id="@+id/love_self_iv"
                    android:layout_width="@dimen/love_head_icon_size"
                    android:layout_height="@dimen/love_head_icon_size"
                    android:layout_marginStart="-8dp"
                    android:layout_toEndOf="@+id/love_other_iv"
                    android:scaleType="centerCrop"
                    app:border_color="#f5f5f5"
                    app:border_width="3dp"
                    app:type="circle"
                    tools:src="@drawable/default_head_yellow" />

                <ImageView
                    android:id="@+id/love_self_connect_iv"
                    android:layout_width="19dp"
                    android:layout_height="19dp"
                    android:layout_below="@+id/love_self_iv"
                    android:layout_alignStart="@+id/love_self_iv"
                    android:layout_alignEnd="@+id/love_self_iv"
                    android:layout_marginTop="-10dp"
                    android:src="@drawable/love_un_connect_icon" />

                <ImageView
                    android:id="@+id/love_other_connect_iv"
                    android:layout_width="19dp"
                    android:layout_height="19dp"
                    android:layout_below="@+id/love_other_iv"
                    android:layout_alignStart="@+id/love_other_iv"
                    android:layout_alignEnd="@+id/love_other_iv"
                    android:layout_marginTop="-10dp"
                    android:src="@drawable/love_un_connect_icon" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/love_fragment_head_rl"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="2dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/love_together_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:ellipsize="end"
                    android:lines="1"
                    android:textColor="@color/text_color_black_nomal"
                    android:textSize="10sp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/love_twoo_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:ellipsize="end"
                    android:lines="1"
                    android:textColor="@android:color/black"
                    android:textSize="10sp" />

            </LinearLayout>

        </RelativeLayout>

        <com.totwoo.totwoo.widget.AxxPagView
            android:id="@+id/pag_bq"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:pag_autoPlay="false"
            app:pag_loop="false"/>

        <ImageView
            android:id="@+id/love_home_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="42dp"
            android:src="@drawable/love_fragment_home_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.82" />


        <LinearLayout
            android:id="@+id/love_totwoo_rl"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:background="@drawable/shape_love_text_bg"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@+id/love_home_iv"
            app:layout_constraintEnd_toStartOf="@+id/love_chat_iv"
            app:layout_constraintStart_toEndOf="@+id/love_home_iv"
            app:layout_constraintTop_toTopOf="@+id/love_home_iv">

            <ImageView
                android:id="@+id/love_totwoo_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="28dp"
                android:src="@drawable/love_fragment_heart" />

            <TextView
                android:id="@+id/love_totwoo_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginEnd="28dp"
                android:text="@string/home_totwoo_holder_send_totwoo_bt_text"
                android:textColor="@color/text_color_black_important" />

        </LinearLayout>


        <ImageView
            android:id="@+id/love_chat_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="42dp"
            android:src="@drawable/love_fragment_talk_icon"
            app:layout_constraintBottom_toBottomOf="@+id/love_home_iv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintTop_toTopOf="@+id/love_home_iv" />



        <!--v2-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/love_message_layout_v2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="206dp"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.totwoo.totwoo.widget.RoundImageView
                android:id="@+id/love_message_head_icon_v2"
                android:layout_width="@dimen/setting_head_icon_size"
                android:layout_height="@dimen/setting_head_icon_size"
                android:layout_marginStart="42dp"
                android:src="@drawable/default_head_yellow"
                app:border_color="@color/layer_bg_white"
                app:border_width="3dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.hjq.shape.view.ShapeTextView
                android:id="@+id/love_message_info_tv_v2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/bq_msg_bg_left"
                android:gravity="center"
                android:paddingHorizontal="12dp"
                android:paddingTop="11dp"
                android:paddingBottom="13dp"
                android:textColor="@color/text_color_black"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="@+id/love_message_head_icon_v2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@+id/love_message_head_icon_v2"
                tools:text="TA is summoning you on Totwoo" />

            <ImageView
                android:id="@+id/love_message_info_iv_v2"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="@+id/love_message_info_tv_v2"
                app:layout_constraintEnd_toEndOf="@+id/love_message_info_tv_v2"
                app:layout_constraintStart_toEndOf="@+id/love_message_info_tv_v2"
                app:layout_constraintTop_toBottomOf="@+id/love_message_info_tv_v2"
                tools:src="@drawable/bq_totwoo_big" />


            <TextView
                android:id="@+id/love_message_date_tv"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="14dp"
                android:background="@drawable/shape_love_info_text_bg"
                android:gravity="center"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:textColor="@color/text_color_black_nomal"
                android:textSize="10sp"
                app:layout_constraintBottom_toBottomOf="@+id/love_message_info_tv_v2"
                app:layout_constraintStart_toEndOf="@+id/love_message_info_tv_v2"
                app:layout_constraintTop_toTopOf="@+id/love_message_info_tv_v2" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.totwoo.totwoo.widget.TopLayerLayout
        android:id="@+id/love_notify_top_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        app:TopTitle="@string/index_t2"
        app:layout_constraintTop_toTopOf="parent" />


</FrameLayout>