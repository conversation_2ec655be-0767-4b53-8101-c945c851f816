<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="bottom"
    android:layoutDirection="ltr"
    android:orientation="vertical">

    <!-- 顶部空白区域，点击可以显示/隐藏控制器 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- 底部控制栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="4dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/exo_controls_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="16dp"
        android:paddingTop="8dp"
        android:paddingRight="16dp"
        android:paddingBottom="8dp">

        <!-- 播放/暂停按钮 -->
        <ImageButton
            android:id="@id/exo_play_pause"
            style="@style/ExoStyledControls.Button.Center.PlayPause"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginRight="8dp" />

        <!-- 进度条 -->
        <com.google.android.exoplayer2.ui.DefaultTimeBar
            android:id="@id/exo_progress"
            android:layout_width="0dp"
            android:layout_height="26dp"
            android:layout_weight="1"
            app:played_color="#FFFFFF"
            app:scrubber_color="#FFFFFF"
            app:unplayed_color="#33FFFFFF" />

        <!-- 全屏按钮（可选） -->
        <ImageButton
            android:id="@id/exo_fullscreen"
            style="@style/ExoStyledControls.Button.Bottom.FullScreen"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginLeft="8dp"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>
