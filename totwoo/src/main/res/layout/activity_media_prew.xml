<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">


    <com.google.android.exoplayer2.ui.PlayerView
        android:id="@+id/media_video_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="invisible"
        app:use_controller="true"
        app:show_buffering="when_playing" />

    <com.totwoo.totwoo.widget.PinchImageView
        android:id="@+id/media_cover_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitCenter" />

    <com.totwoo.totwoo.widget.ExpandableTextView
        android:id="@+id/media_info_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#BB0B0B0B"
        android:lineSpacingMultiplier="1.4"
        android:padding="14dp"
        android:textColor="#ff979797"
        android:textSize="13sp"
        android:visibility="gone"
        app:expanded="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:minLines="2" />

    <ProgressBar
        android:id="@+id/media_progressbar"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/totwoo_topbar_layout"
        layout="@layout/totwoo_topbar_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/activity_actionbar_height"
        android:background="@color/black"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 临时调试按钮 -->
    <Button
        android:id="@+id/debug_play_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="播放/暂停"
        android:background="#88000000"
        android:textColor="#FFFFFF"
        android:layout_margin="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>