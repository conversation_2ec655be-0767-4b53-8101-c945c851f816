package com.totwoo.totwoo.keepalive.companion

import android.app.Activity
import android.bluetooth.BluetoothAdapter
import android.companion.AssociationInfo
import android.companion.AssociationRequest
import android.companion.BluetoothDeviceFilter
import android.companion.CompanionDeviceManager
import android.content.Context
import android.content.IntentSender
import android.os.Build
import android.text.TextUtils
import androidx.annotation.RequiresApi
import com.blankj.utilcode.util.Utils
import com.tencent.mars.xlog.Log
import com.totwoo.library.util.LogUtils
import com.totwoo.totwoo.utils.PreferencesUtils
import java.util.regex.Pattern

/**
 * CompanionDevice配套设备管理器 - 单例模式
 *
 * 核心功能：
 * 1. 基于已配对设备的自动关联 - autoAssociateWithPairedDevice()
 * 2. 申请后台运行权限 - requestNotificationAccess()
 * 3. 设备存在状态监听 - startObservingDevicePresence()
 * 4. 停止设备监听 - stopObservingDevicePresence()
 * 5. 解除设备关联 - disassociateDevice()
 *
 * 使用方式：
 * CompanionDeviceHelper.init(context)
 * CompanionDeviceHelper.autoAssociateWithPairedDevice(activity, deviceName, deviceMac, callback)
 */
object CompanionDeviceHelper {

    private const val TAG = "CompanionDeviceHelper"
    private const val REQUEST_CODE_COMPANION_DEVICE = 1001
    private const val PREF_COMPANION_DEVICE_MAC = "companion_device_mac"

    // 私有变量
    private var companionDeviceManager: CompanionDeviceManager? = null
    private var currentCallback: CompanionDeviceCallback? = null

    /**
     * 初始化CompanionDevice管理器
     */
    fun init() {
        if (isCompanionDeviceSupported()) {
            companionDeviceManager = Utils.getApp().getSystemService(CompanionDeviceManager::class.java)
            Log.d(TAG, "CompanionDeviceHelper初始化完成")
        } else {
            Log.w(TAG, "当前系统版本不支持CompanionDevice API")
        }
    }

    /**
     * 12以上配对模式
     * 检查是否支持CompanionDevice功能
     */
    fun isCompanionDeviceSupported(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
    }

    /**
     * 检查设备是否已关联
     * @param deviceMac 可选参数，指定要检查的设备MAC地址。如果为空则检查是否有任何关联设备
     * @return true表示设备已关联
     */
    fun isDeviceAssociated(deviceMac: String? = null): Boolean {
        if (!isCompanionDeviceSupported()) return false

        return try {
            val manager = companionDeviceManager ?: return false

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ 使用新API
                val associations = manager.myAssociations
                if (deviceMac.isNullOrEmpty()) {
                    associations.isNotEmpty()
                } else {
                    associations.any { association ->
                        val associatedMac = getDeviceMacAddress(association)
                        associatedMac.equals(deviceMac, ignoreCase = true)
                    }
                }
            } else {
                // Android 8-12 使用旧API
                try {
                    @Suppress("DEPRECATION")
                    val associations = manager.associations
                    if (deviceMac.isNullOrEmpty()) {
                        associations.isNotEmpty()
                    } else {
                        // 对于Android 8-12，associations返回的是String类型的MAC地址列表
                        associations.any { macAddress ->
                            macAddress.equals(deviceMac, ignoreCase = true)
                        }
                    }
                } catch (e: Exception) {
                    LogUtils.w(TAG, "使用旧API检查关联失败，回退到本地检查: ${e.message}")
                    // 如果API调用失败，检查本地保存的关联信息
                    val savedMac = PreferencesUtils.getString(Utils.getApp(), PREF_COMPANION_DEVICE_MAC, "")
                    if (deviceMac.isNullOrEmpty()) {
                        savedMac.isNotEmpty()
                    } else {
                        savedMac.equals(deviceMac, ignoreCase = true)
                    }
                }
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "检查设备关联状态失败: ${e.message}")
            false
        }
    }

    /**
     * 基于已配对设备自动关联配套设备
     * @param activity 当前Activity
     * @param deviceName 设备名称（可能重复，仅作参考）
     * @param deviceMac 设备MAC地址（唯一标识）
     * @param callback 回调接口
     */
    fun autoAssociateWithPairedDevice(
        activity: Activity,
        deviceMac: String,
        callback: CompanionDeviceCallback
    ) {
        if (!isCompanionDeviceSupported()) {
            callback.onError("当前系统版本不支持CompanionDevice API")
            return
        }

        // 严格验证MAC地址格式和设备名称
        if (TextUtils.isEmpty(deviceMac) || !isValidMacAddress(deviceMac)) {
            callback.onError("设备MAC地址格式无效: $deviceMac")
            return
        }


        currentCallback = callback

        try {
            // 检查是否已经关联了这个特定设备
            if (isDeviceAssociated(deviceMac)) {
                LogUtils.d(TAG, "设备已关联，直接启动监听: $deviceMac")
                saveAssociationInfo(deviceMac)
                callback.onDeviceAssociated(deviceMac)
                return
            }

            // 创建关联请求
            val deviceFilter = BluetoothDeviceFilter.Builder()
                .setAddress(deviceMac)
                .build()

            val associationRequest = AssociationRequest.Builder()
                .addDeviceFilter(deviceFilter)
                .setSingleDevice(true)
                .build()

            // 发起关联请求
            companionDeviceManager?.associate(
                associationRequest,
                object : CompanionDeviceManager.Callback() {
                    override fun onDeviceFound(chooserLauncher: IntentSender) {
                        LogUtils.d(TAG, "找到配套设备，启动选择器")
                        try {
                            activity.startIntentSenderForResult(
                                chooserLauncher,
                                REQUEST_CODE_COMPANION_DEVICE,
                                null, 0, 0, 0
                            )
                        } catch (e: Exception) {
                            LogUtils.e(TAG, "启动设备选择器失败: ${e.message}")
                            callback.onError("启动设备选择器失败: ${e.message}")
                        }
                    }

                    override fun onFailure(error: CharSequence?) {
                        LogUtils.e(TAG, "配套设备关联失败: $error")
                        callback.onError("配套设备关联失败: $error")
                    }
                },
                null
            )

        } catch (e: Exception) {
            LogUtils.e(TAG, "自动关联配套设备失败: ${e.message}")
            callback.onError("自动关联配套设备失败: ${e.message}")
        }
    }

    /**
     * 申请后台运行权限（通知访问权限）
     */
    fun requestNotificationAccess(context: Context) {

    }




    /**
     * 开始监听设备存在状态
     * 需要 Android 12+ (API 31+) 支持
     */
    @RequiresApi(Build.VERSION_CODES.S)
    fun startObservingDevicePresence() {
        try {
            companionDeviceManager?.let { manager ->
                // 使用安全的方式获取关联的MAC地址列表
                val associatedMacs = getAssociationsSafely()
                if (associatedMacs.isEmpty()) {
                    LogUtils.w(TAG, "没有找到配套设备关联，无法开始监听")
                    return
                }

                LogUtils.d(TAG, "开始监听配套设备存在状态，关联数量: ${associatedMacs.size}")

                for (deviceMac in associatedMacs) {
                    if (deviceMac.isNotEmpty() && isValidMacAddress(deviceMac)) {
                        try {
                            manager.startObservingDevicePresence(deviceMac)
                            Log.d(TAG, "成功启动设备监听: $deviceMac")
                        } catch (e: Exception) {
                            Log.e(TAG, "启动设备监听失败 $deviceMac: ${e.message}")
                        }
                    }
                }
            } ?: LogUtils.e(TAG, "CompanionDeviceManager未初始化")
        } catch (e: Exception) {
            LogUtils.e(TAG, "开始监听设备存在状态失败: ${e.message}")
        }
    }

    /**
     * 停止监听设备存在状态
     */
    @RequiresApi(Build.VERSION_CODES.S)
    fun stopObservingDevicePresence() {

        try {
            companionDeviceManager?.let { manager ->
                val associatedMacs = getAssociationsSafely()
                if (associatedMacs.isEmpty()) {
                    LogUtils.d(TAG, "没有配套设备关联，无需停止监听")
                    return
                }


                for (deviceMac in associatedMacs) {
                    if (deviceMac.isNotEmpty() && isValidMacAddress(deviceMac)) {
                        try {
                            manager.stopObservingDevicePresence(deviceMac)
                            Log.d(TAG, "成功停止设备监听: $deviceMac")
                        } catch (e: Exception) {
                            Log.e(TAG, "停止设备监听失败 $deviceMac: ${e.message}")
                        }
                    }
                }
            } ?: LogUtils.e(TAG, "CompanionDeviceManager未初始化")
        } catch (e: Exception) {
            LogUtils.e(TAG, "停止监听设备存在状态失败: ${e.message}")
        }
    }

    /**
     * 取消配套设备关联
     */
    fun disassociateDevice() {
        if (!isCompanionDeviceSupported()) {
            LogUtils.w(TAG, "当前系统版本不支持CompanionDevice API")
            return
        }

        try {
            companionDeviceManager?.let { manager ->
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    // Android 13+ 使用新API
                    val associations = manager.myAssociations
                    if (associations.isEmpty()) {
                        LogUtils.d(TAG, "没有配套设备关联，无需取消")
                        return
                    }

                    LogUtils.d(TAG, "开始取消配套设备关联，关联数量: ${associations.size}")

                    for (association in associations) {
                        try {
                            val deviceMac = association.deviceMacAddress?.toString() ?: "未知"
                            val associationId = association.id
                            LogUtils.d(TAG, "取消关联设备: MAC=$deviceMac, ID=$associationId")
                            manager.disassociate(associationId)
                        } catch (e: Exception) {
                            LogUtils.e(TAG, "取消单个关联失败: ${e.message}")
                        }
                    }
                } else {
                    // Android 8-12: 使用本地缓存的MAC地址
                    val savedMac = PreferencesUtils.getString(Utils.getApp(), PREF_COMPANION_DEVICE_MAC, "")
                    if (savedMac.isNotEmpty() && isValidMacAddress(savedMac)) {
                        LogUtils.d(TAG, "使用本地缓存的MAC地址取消关联: $savedMac")
                        try {
                            @Suppress("DEPRECATION")
                            manager.disassociate(savedMac)
                            LogUtils.d(TAG, "成功取消关联: $savedMac")
                        } catch (e: Exception) {
                            LogUtils.e(TAG, "使用缓存MAC取消关联失败: ${e.message}")
                        }
                    } else {
                        // 如果没有缓存，尝试获取关联列表（但不依赖反射）
                        try {
                            val associations = getAssociations()
                            if (associations.isNotEmpty()) {
                                LogUtils.d(TAG, "开始取消配套设备关联，关联数量: ${associations.size}")
                                for (association in associations) {
                                    try {
                                        val deviceMac = getDeviceMacAddress(association)
                                        if (deviceMac.isNotEmpty() && isValidMacAddress(deviceMac)) {
                                            @Suppress("DEPRECATION")
                                            manager.disassociate(deviceMac)
                                            LogUtils.d(TAG, "成功取消关联: $deviceMac")
                                        }
                                    } catch (e: Exception) {
                                        LogUtils.e(TAG, "取消单个关联失败: ${e.message}")
                                    }
                                }
                            } else {
                                LogUtils.d(TAG, "没有找到配套设备关联，无需取消")
                            }
                        } catch (e: Exception) {
                            LogUtils.e(TAG, "获取关联列表失败，无法取消关联: ${e.message}")
                        }
                    }
                }

                // 清除本地保存的关联信息
                clearAssociationInfo()
                LogUtils.d(TAG, "配套设备关联处理完成")

            } ?: LogUtils.e(TAG, "CompanionDeviceManager未初始化")
        } catch (e: Exception) {
            LogUtils.e(TAG, "取消配套设备关联失败: ${e.message}")
            // 即使失败也要清除本地缓存，避免状态不一致
            try {
                clearAssociationInfo()
            } catch (e2: Exception) {
                LogUtils.e(TAG, "清除本地关联信息也失败: ${e2.message}")
            }
        }
    }

    /**
     * 处理关联结果
     */
    fun handleAssociationResult(resultCode: Int, deviceMac: String) {
        if (resultCode == Activity.RESULT_OK) {
            LogUtils.d(TAG, "配套设备关联成功: $deviceMac")
            saveAssociationInfo(deviceMac)
            currentCallback?.onDeviceAssociated(deviceMac)
        } else {
            LogUtils.w(TAG, "配套设备关联被取消")
            currentCallback?.onError("用户取消了设备关联")
        }
        currentCallback = null
    }

    // 私有辅助方法

    /**
     * 获取关联列表（兼容不同Android版本）- 增强版
     */
    private fun getAssociations(): List<AssociationInfo> {
        return try {
            companionDeviceManager?.let { manager ->
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    // Android 13+ 使用新API
                    manager.myAssociations
                } else {
                    try {
                        @Suppress("DEPRECATION")
                        val associations = manager.associations

                        // 只处理确定是AssociationInfo类型的对象，避免类型转换异常
                        val safeAssociations = mutableListOf<AssociationInfo>()
                        for (association in associations) {
                            if (association is AssociationInfo) {
                                safeAssociations.add(association)
                            } else {
                                // 跳过非AssociationInfo类型
                                LogUtils.d(TAG, "跳过非AssociationInfo类型的关联: ${association.javaClass.simpleName}")
                            }
                        }
                        safeAssociations
                    } catch (e: Exception) {
                        LogUtils.w(TAG, "使用旧API获取关联失败: ${e.message}")
                        emptyList()
                    }
                }
            } ?: emptyList()
        } catch (e: Exception) {
            LogUtils.e(TAG, "获取关联列表失败: ${e.message}")
            emptyList()
        }
    }

    /**
     * 安全地获取设备MAC地址（无反射版本）
     * 避免Android高版本反射限制导致的崩溃
     */
    private fun getDeviceMacAddress(associationInfo: AssociationInfo): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ 使用新API
                associationInfo.deviceMacAddress?.toString() ?: ""
            } else {
                // Android 8-12: 使用toString()解析，避免反射
                val stringValue = associationInfo.toString()
                LogUtils.d(TAG, "AssociationInfo.toString(): $stringValue")

                // 从toString()中提取MAC地址
                val macPattern = Pattern.compile("([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})")
                val matcher = macPattern.matcher(stringValue)
                if (matcher.find()) {
                    val extractedMac = matcher.group()
                    if (!extractedMac.isNullOrEmpty() && isValidMacAddress(extractedMac)) {
                        LogUtils.d(TAG, "从toString()中提取到MAC地址: $extractedMac")
                        return extractedMac
                    }
                }

                // 回退到本地缓存
                val savedMac = PreferencesUtils.getString(Utils.getApp(), PREF_COMPANION_DEVICE_MAC, "")
                if (savedMac.isNotEmpty() && isValidMacAddress(savedMac)) {
                    LogUtils.d(TAG, "使用本地缓存的MAC地址: $savedMac")
                    return savedMac
                }

                LogUtils.w(TAG, "无法获取MAC地址，所有方法都失败")
                ""
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "获取设备MAC地址失败: ${e.message}")
            // 最后的回退：使用本地缓存
            try {
                val savedMac = PreferencesUtils.getString(Utils.getApp(), PREF_COMPANION_DEVICE_MAC, "")
                if (savedMac.isNotEmpty() && isValidMacAddress(savedMac)) {
                    LogUtils.d(TAG, "异常回退：使用本地缓存的MAC地址: $savedMac")
                    return savedMac
                }
            } catch (e2: Exception) {
                LogUtils.e(TAG, "连本地缓存都无法访问: ${e2.message}")
            }
            ""
        }
    }

    /**
     * 安全地获取关联ID（无反射版本）
     * 避免Android高版本反射限制导致的崩溃
     */
    private fun getAssociationId(associationInfo: AssociationInfo): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ 使用新API
                associationInfo.id
            } else {
                // Android 8-12: 使用hashCode()作为ID，避免反射
                val hashId = associationInfo.hashCode()
                LogUtils.d(TAG, "使用hashCode作为关联ID: $hashId")

                // 确保ID为正数
                val safeId = if (hashId < 0) -hashId else hashId

                // 从toString()中尝试提取数字ID
                val stringValue = associationInfo.toString()
                val idPattern = Pattern.compile("id[=:]\\s*(\\d+)")
                val matcher = idPattern.matcher(stringValue)
                if (matcher.find()) {
                    try {
                        val extractedId = matcher.group(1)?.toInt() ?: safeId
                        LogUtils.d(TAG, "从toString()中提取到ID: $extractedId")
                        return extractedId
                    } catch (e: NumberFormatException) {
                        LogUtils.w(TAG, "提取的ID格式错误，使用hashCode: $safeId")
                    }
                }

                safeId
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "获取关联ID失败: ${e.message}")
            // 回退：使用对象的hashCode
            try {
                val fallbackId = associationInfo.hashCode()
                val safeId = if (fallbackId < 0) -fallbackId else fallbackId
                LogUtils.d(TAG, "异常回退：使用hashCode作为ID: $safeId")
                safeId
            } catch (e2: Exception) {
                LogUtils.e(TAG, "连hashCode都无法获取: ${e2.message}")
                -1
            }
        }
    }

    private fun saveAssociationInfo(deviceMac: String) {
        PreferencesUtils.put(Utils.getApp(), PREF_COMPANION_DEVICE_MAC, deviceMac)
    }

    private fun clearAssociationInfo() {
        PreferencesUtils.put(Utils.getApp(), PREF_COMPANION_DEVICE_MAC, "")
    }

    /**
     * 验证MAC地址格式是否正确
     * @param macAddress MAC地址字符串
     * @return true表示格式正确
     */
    private fun isValidMacAddress(macAddress: String): Boolean {
        if (macAddress.isEmpty()) return false
        return BluetoothAdapter.checkBluetoothAddress(macAddress.uppercase())
    }

    /**
     * 安全地获取关联列表（增强版本兼容性）
     */
    @RequiresApi(Build.VERSION_CODES.O)
    private fun getAssociationsSafely(): List<String> {
        return try {
            companionDeviceManager?.let { manager ->
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    // Android 13+ 使用新API
                    manager.myAssociations.mapNotNull { association ->
                        getDeviceMacAddress(association).takeIf { it.isNotEmpty() }
                    }
                } else {
                    // Android 8-12 使用旧API
                    try {
                        @Suppress("DEPRECATION")
                        val associations = manager.associations
                        associations.filterIsInstance<String>().filter { it.isNotEmpty() }
                    } catch (e: Exception) {
                        LogUtils.w(TAG, "获取关联列表失败，使用本地缓存: ${e.message}")
                        // 回退到本地保存的信息
                        val savedMac = PreferencesUtils.getString(Utils.getApp(), PREF_COMPANION_DEVICE_MAC, "")
                        if (savedMac.isNotEmpty()) listOf(savedMac) else emptyList()
                    }
                }
            } ?: emptyList()
        } catch (e: Exception) {
            LogUtils.e(TAG, "安全获取关联列表失败: ${e.message}")
            emptyList()
        }
    }

    /**
     * 配对回调接口
     */
    interface CompanionDeviceCallback {
        fun onDeviceAssociated(deviceMac: String)
        fun onError(error: String)
    }
}
