package com.totwoo.totwoo.keepalive

import android.bluetooth.BluetoothAdapter
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import com.blankj.utilcode.util.ThreadUtils
import com.totwoo.library.util.LogUtils
import com.totwoo.totwoo.ToTwooApplication
import com.totwoo.totwoo.ble.BleParams
import com.totwoo.totwoo.ble.BluetoothManage
import com.totwoo.totwoo.service.KeepAliveService

/**
 * 保活系统事件广播接收器 - 适配Android高版本限制
 *
 * 功能：
 * 1. 静态注册：开机启动、应用更新、电源状态、蓝牙状态、网络状态
 * 2. 触发保活机制恢复
 */
class SystemReceiverKeepAlive : BroadcastReceiver() {

    companion object {
        private const val TAG = "SystemReceiverKeepAlive"

        // 防抖机制 - 避免频繁触发
        private var lastScreenEventTime = 0L
        private var lastBluetoothEventTime = 0L
        private var lastNetworkEventTime = 0L
        private const val EVENT_DEBOUNCE_INTERVAL = 2000L // 3秒防抖
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent?.action == null) return
        val action = intent.action!!
        val currentTime = System.currentTimeMillis()
        // 防抖检查
        if (isEventDebounced(action, currentTime)) {
            LogUtils.d(TAG, "事件防抖，跳过处理: $action")
            return
        }
        LogUtils.d(TAG, "📡 收到系统广播: $action")

        when (action) {
            Intent.ACTION_SCREEN_ON -> {
                LogUtils.d(TAG, "屏幕点亮")
                checkAndRestartServices(context, "SCREEN_ON")
            }

            Intent.ACTION_SCREEN_OFF -> {
                LogUtils.d(TAG, "屏幕关闭")
                checkAndRestartServices(context, "SCREEN_OFF")
            }


            ConnectivityManager.CONNECTIVITY_ACTION -> {
                LogUtils.d(TAG, "网络状态变化")
                checkAndRestartServices(context, "NETWORK_CHANGED")
            }


            BluetoothAdapter.ACTION_STATE_CHANGED -> {
                val state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR)
                LogUtils.d(TAG, "蓝牙状态变化: $state")
                handleBluetoothStateChanged(context, state)
            }

            else -> {
                LogUtils.d(TAG, "未处理的广播事件: $action")
            }
        }
    }



    /**
     * 检查并重启服务 - 直接启动
     */
    private fun checkAndRestartServices(context: Context, trigger: String) {
        try {
            val serviceIntent = Intent(context, KeepAliveService::class.java).apply {
                action = BleParams.ACTION_KEEP_ALIVE
                putExtra("trigger", trigger)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }

            LogUtils.d(TAG, "服务启动成功: $trigger")
        } catch (e: Exception) {
            LogUtils.e(TAG, "服务启动失败: ${e.message}")
        }
    }

    /**
     * 检查并启动服务 - 用于开机启动
     */
    private fun checkAndStartServices(context: Context, trigger: String) {
        try {
            val serviceIntent = Intent(context, KeepAliveService::class.java).apply {
                action = BleParams.ACTION_START
                putExtra("trigger", trigger)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }

            LogUtils.d(TAG, "服务启动成功: $trigger")
        } catch (e: Exception) {
            LogUtils.e(TAG, "服务启动失败: ${e.message}")
        }
    }

    /**
     * 处理蓝牙状态变化 - 迁移自 KeepAliveService.mBTStateChangedReceiver
     */
    private fun handleBluetoothStateChanged(context: Context, state: Int) {
        // 检查是否有配对的蓝牙首饰设备
        if (!BleParams.isBluetoothJewelry(null)) {
            LogUtils.d(TAG, "没有配对的蓝牙首饰设备，跳过蓝牙状态处理")
            return
        }

        when (state) {
            BluetoothAdapter.STATE_ON -> {
                // 延迟500ms处理，与原逻辑保持一致
                ThreadUtils.getMainHandler().postDelayed({
                    BluetoothManage.getInstance().blueToothTurnOn(ToTwooApplication.baseContext)
                },500)
            }

            BluetoothAdapter.STATE_OFF -> {
                LogUtils.d(TAG, "蓝牙已关闭")
                BluetoothManage.getInstance().blueToothTurnOff()
            }

            else -> {
                LogUtils.d(TAG, "蓝牙状态变化: $state (无需特殊处理)")
            }
        }

        // 同时触发服务重启逻辑（保持原有的保活机制）
        checkAndRestartServices(context, "BLUETOOTH_STATE_CHANGED")
    }

    /**
     * 检查事件是否需要防抖
     */
    private fun isEventDebounced(action: String, currentTime: Long): Boolean {
        return when (action) {
            Intent.ACTION_SCREEN_ON,
            Intent.ACTION_SCREEN_OFF -> {
               return false
            }

            BluetoothAdapter.ACTION_STATE_CHANGED -> {
                return false
            }

            ConnectivityManager.CONNECTIVITY_ACTION -> {
                if (currentTime - lastNetworkEventTime < EVENT_DEBOUNCE_INTERVAL) {
                    lastScreenEventTime = currentTime
                    true
                } else {
                    lastNetworkEventTime = currentTime
                    false
                }
            }

            else -> false
        }
    }
}
