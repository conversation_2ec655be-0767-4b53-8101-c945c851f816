package com.totwoo.totwoo.activity;

import static com.totwoo.totwoo.bean.PermissionItemBean.APPLIED_QUICK_FREEZING;
import static com.totwoo.totwoo.bean.PermissionItemBean.PER_BACKGROUND_TRAFFIC_ALLOWEDB;
import static com.totwoo.totwoo.bean.PermissionItemBean.PER_BLANK_PASS;
import static com.totwoo.totwoo.bean.PermissionItemBean.PER_BLE;
import static com.totwoo.totwoo.bean.PermissionItemBean.PER_CLOSE_NO_DISTURB;
import static com.totwoo.totwoo.bean.PermissionItemBean.PER_COMPANION_DEVICE;
import static com.totwoo.totwoo.bean.PermissionItemBean.PER_IGNORING_BATTERY_OPT;
import static com.totwoo.totwoo.bean.PermissionItemBean.PER_KEEP_BACKGROUND_RUN;
import static com.totwoo.totwoo.bean.PermissionItemBean.PER_NOTIFICATION;
import static com.totwoo.totwoo.bean.PermissionItemBean.PER_PAUSE_IDLE_APP;
import static com.totwoo.totwoo.bean.PermissionItemBean.PER_SAVE_POWER;

import android.Manifest;
import android.app.AlarmManager;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.LayoutInflater;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.RomUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.tencent.mars.xlog.Log;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.bean.PermissionItemBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.BluetoothManage;
import com.totwoo.totwoo.databinding.ActivitySetPermissionBinding;
import com.totwoo.totwoo.keepalive.companion.CompanionDeviceHelper;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.widget.CommonMiddleDialog;
import com.totwoo.totwoo.widget.PrivacyProtectionDialog;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @des:
 * @date 2024/9/19 15:37
 */
public class SetPermissionActivity extends BaseActivity {

    private static final String TAG = "SetPermissionActivity";
    private ActivitySetPermissionBinding binding;
    private List<PermissionItemBean> permissionList = new ArrayList<>();
    private PermissionAdapter permissionAdapter;
    private int currentPosition = 0;
    private String macAddress;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivitySetPermissionBinding.inflate(LayoutInflater.from(this));
        setContentView(binding.getRoot());
        initView();
        initData();
    }


    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_black);
        setTopLeftOnclik(v -> finish());
        setTopTitle(R.string.page_permission_authority_management);
        setSpinState(false);
    }

    private void initData() {
        // 添加权限项
        permissionList.add(new PermissionItemBean(
                PER_BLE,
                (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) ?
                        getString(R.string.page_permission_ble_nearby) :
                        getString(R.string.page_permission_ble_location),
                (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) ?
                        getString(R.string.page_permission_ble_nearby_des) :
                        getString(R.string.page_permission_ble_location_des),
                PermissionUtil.hasBlePermission() ? 1 : 0
        ));

        // 通知权限 ，oppo ,vivo 默认锁屏需要单独开启
        if (RomUtils.isOppo() || RomUtils.isVivo()) {
            permissionList.add(new PermissionItemBean(
                    PER_NOTIFICATION,
                    getString(R.string.page_permission__notification),
                    getString(R.string.page_permission__notification_des),
                    -1
            ));
        } else {
            permissionList.add(new PermissionItemBean(
                    PER_NOTIFICATION,
                    getString(R.string.page_permission__notification),
                    getString(R.string.page_permission__notification_des),
                    PermissionUtil.isAppNotificationEnabled() ? 1 : 0
            ));
        }


        // 只有23以上才有电量优化
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            permissionList.add(new PermissionItemBean(
                    PER_IGNORING_BATTERY_OPT,
                    getString(R.string.page_permission_ignoring_battery_optimization),
                    getString(R.string.page_permission_ignoring_battery_optimization_des),
                    CommonUtils.isIgnoringBatteryOptimizations(this) ? 1 : 0
            ));
        }

        // Android 12以上需要精确闹钟权限
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
//            boolean hasPermission = hasExactAlarmPermission();
//            String description = hasPermission ?
//                "已授权精确闹钟权限，蓝牙重连和消息推送响应更及时" :
//                "建议授权精确闹钟权限，可显著提升蓝牙保活效果和消息响应速度";
//
//            permissionList.add(new PermissionItemBean(
//                    PermissionItemBean.PER_EXACT_ALARM,
//                    "精确闹钟权限" + (hasPermission ? " ✅" : " ⚠️"),
//                    description,
//                    hasPermission ? 1 : 0
//            ));
//        }


        if (CompanionDeviceHelper.INSTANCE.isCompanionDeviceSupported() && !TextUtils.isEmpty(macAddress)) {
            permissionList.add(new PermissionItemBean(
                    PER_COMPANION_DEVICE,
                    "保活配对",
                    "允许保活配对，可以让蓝牙和设备更好的保持链接",
                    CompanionDeviceHelper.INSTANCE.isDeviceAssociated(macAddress) ? 1 : 0
            ));
        }

        // 三星省电不限制，其他品牌需要单独设置
        if (!RomUtils.isSamsung()) {
            permissionList.add(new PermissionItemBean(
                    PER_KEEP_BACKGROUND_RUN,
                    getString(R.string.keep_totwoo_background_run),
                    allowBackgroundActivities(),
                    -1
            ));
        }

        // 小米 MIUI 12 或以上
        if (RomUtils.isXiaomi() && CommonUtils.isMIUI12OrAbove()) {
            permissionList.add(new PermissionItemBean(
                    PER_BLANK_PASS,
                    getString(R.string.page_permission_blank_pass),
                    getString(R.string.page_permission_blank_pass_des),
                    -1
            ));
        }

        // 低版本OPPO 应用速冻 ,https://support.oppo.com/cn/answer/?aid=SI2106885
        if (RomUtils.isOppo() && CommonUtils.isColorOS51To7()) {
            permissionList.add(new PermissionItemBean(
                    APPLIED_QUICK_FREEZING,
                    "应用速冻",
                    "系统智能判断应用是否处于闲置并将其速冻，导致无法接收totwoo消息，ColorOS 5.1-7版本：前往设置>电池，关闭应用速冻。",
                    -1
            ));
        }

        // 省电模式
        if (PermissionUtil.isPowerSaveModeCompat()) {
            permissionList.add(new PermissionItemBean(
                    PER_SAVE_POWER,
                    getString(R.string.page_permission_save_power),
                    getString(R.string.page_permission_power_desc),
                    PermissionUtil.isPowerSaveModeCompat() ? 0 : 1
            ));
        }

        // 免打扰模式
        if (PermissionUtil.getNotificationPolicyAccess()) {
            permissionList.add(new PermissionItemBean(
                    PER_CLOSE_NO_DISTURB,
                    getString(R.string.page_permission_close_no_disturb),
                    getString(R.string.page_permission_close_no_disturb_des),
                    PermissionUtil.getNotificationPolicyAccess() ? 0 : 1
            ));
        }

        // 只有24以上才有禁止后台流量
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N &&
                !PermissionUtil.getRestrictBackgroundStatus() &&
                !RomUtils.isXiaomi()) {
            permissionList.add(new PermissionItemBean(
                    PER_BACKGROUND_TRAFFIC_ALLOWEDB,
                    getString(R.string.page_permission_background_traffic_allowed),
                    getString(R.string.page_permission_background_traffic_allowed_des),
                    PermissionUtil.getRestrictBackgroundStatus() ? 1 : 0
            ));
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            for (int index = 0; index < permissionList.size(); index++) {
                PermissionItemBean permissionItem = permissionList.get(index);
                if (PER_IGNORING_BATTERY_OPT == permissionItem.getId()) {
                    currentPosition = index;
                }
            }
            PermissionUtil.requestIgnoreBatteryOptimizations(this);
        }
    }

    private String allowBackgroundActivities() {
        if (RomUtils.isHuawei()) {
            return getString(R.string.keep_totwoo_huawei_background_run_des);
        } else if (RomUtils.isXiaomi()) {
            return getString(R.string.keep_totwoo_xiaomi_background_run_des);
        } else if (RomUtils.isOppo()) {
            return getString(R.string.keep_totwoo_oppo_background_run_des);
        } else if (RomUtils.isVivo()) {
            return getString(R.string.keep_totwoo_vivo_background_run_des);
        } else {
            return getString(R.string.keep_totwoo_background_run_des);
        }
    }

    private void initView() {
        macAddress = PreferencesUtils.getString(this, BleParams.PAIRED_BLE_ADRESS_TAG, "");

        binding.permissionListRv.setLayoutManager(new LinearLayoutManager(this));
        permissionAdapter = new PermissionAdapter();
        binding.permissionListRv.setAdapter(permissionAdapter);
        permissionAdapter.setNewData(permissionList);

        permissionAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            PermissionItemBean item = permissionAdapter.getItem(position);
            if (item != null) {
//                if (item.getPermissionState() == 1) {
//                    return;
//                }
                currentPosition = position;
                switch (item.getId()) {
                    case PER_NOTIFICATION:
                        if (RomUtils.isOppo() || RomUtils.isVivo()) {
                            //跳转引导界面
                            startActivity(new Intent(this, NotificationGuideActivity.class));
                        } else {
                            PermissionUtil.gotoNotificationSetting(this);
                        }
                        break;
                    case PER_BLE:
                        if (!PermissionUtil.hasSystemLocation()) {
                            startActivityForResult(new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS), PermissionUtil.REQUEST_OPEN_SYS_LOCATION);
                        } else {
                            requestBluetoothPermission(item, position);
                        }
                        break;
                    case PER_BLANK_PASS:
                        PermissionUtil.jumpAppDetail(this, PermissionUtil.REQUEST_MI_BLANK_PASS);
                        break;
                    case PER_SAVE_POWER:
                        PermissionUtil.gotoPowerSetting(this, PermissionUtil.REQUEST_OPEN_BATTERY_SETTINGS);
                        break;
                    case PER_IGNORING_BATTERY_OPT:
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                            PermissionUtil.requestIgnoreBatteryOptimizations(this);
                        }
                        break;
                    case PER_KEEP_BACKGROUND_RUN:
                        PermissionUtil.keepRunningInTheBackground(this);
                        break;
                    case PER_BACKGROUND_TRAFFIC_ALLOWEDB:
                        PermissionUtil.setBackgroundData(this);
                        break;
                    case PER_PAUSE_IDLE_APP:
                        PermissionUtil.jumpAppDetail(this, PermissionUtil.REQUEST_PAUSE_IDLE_APPLICATION);
                        break;
                    case PER_CLOSE_NO_DISTURB:
                        PermissionUtil.gotoZenModeSet(this);
                        break;
//                    case PER_EXACT_ALARM:
//                        requestExactAlarmPermission();
//                        break;
                    case PER_COMPANION_DEVICE:
                        if (CompanionDeviceHelper.INSTANCE.isDeviceAssociated(macAddress)) {
                            BluetoothManage.getInstance().clearCompanionDeviceAssociation();
                            permissionList.get(currentPosition).setPermissionState(0);
                            permissionAdapter.notifyItemChanged(currentPosition);
                        } else {
                            BluetoothManage.getInstance().autoAssociateWithPairedDevice(this,
                                    macAddress, new CompanionDeviceHelper.CompanionDeviceCallback() {
                                        @Override
                                        public void onDeviceAssociated(@NonNull String deviceMac) {
                                            Log.d(TAG, "CompanionDevice配对成功: " + deviceMac);
                                            permissionList.get(currentPosition).setPermissionState(1);
                                            permissionAdapter.notifyItemChanged(currentPosition);
                                            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                                                CompanionDeviceHelper.INSTANCE.startObservingDevicePresence();
                                            }
                                        }

                                        @Override
                                        public void onError(@NonNull String error) {
                                            Log.e("JewelryConnectActivity", "CompanionDevice配对失败: " + error);
                                        }
                                    });
                        }
                        break;
                }
            }
        });

        // 设置底部帮助说明点击事件
        binding.tvHelpDetail.setOnClickListener(v -> showPrivacyProtectionDialog());
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (currentPosition == 0) {
            permissionList.get(currentPosition).setPermissionState(
                    PermissionUtils.isGranted(getConnectPermission().toArray(new String[0])) ? 1 : 0);
            permissionAdapter.notifyItemChanged(currentPosition);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case PermissionUtil.REQUEST_OPEN_SYS_LOCATION:
                requestBluetoothPermission(permissionList.get(currentPosition), currentPosition);
                break;

            case PermissionUtil.RESULT_NOTIFICATION:
                permissionList.get(currentPosition).setPermissionState(PermissionUtil.isAppNotificationEnabled() ? 1 : 0);
                permissionAdapter.notifyItemChanged(currentPosition);
                break;

            case PermissionUtil.REQUEST_OPEN_BATTERY_SETTINGS:
                permissionList.get(currentPosition).setPermissionState(
                        PermissionUtil.isPowerSaveModeCompat() ? 0 : 1);
                permissionAdapter.notifyItemChanged(currentPosition);
                break;

            case PermissionUtil.REQUEST_BATTERY_WHITELIST:
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    permissionList.get(currentPosition).setPermissionState(
                            CommonUtils.isIgnoringBatteryOptimizations(this) ? 1 : 0);
                }
                permissionAdapter.notifyItemChanged(currentPosition);
                break;
            case PermissionUtil.RESULT_ZEN_MODE:
                permissionList.get(currentPosition).setPermissionState(
                        PermissionUtil.getNotificationPolicyAccess() ? 0 : 1);
                permissionAdapter.notifyItemChanged(currentPosition);
                break;
            case 1001: //
                // 更新精确闹钟权限状态
                handleCompanionDeviceResult(resultCode, data);
                break;
            default:
        }
    }


    /**
     * 处理CompanionDevice配对结果
     */
    private void handleCompanionDeviceResult(int resultCode, @Nullable Intent data) {
        String deviceMac = PreferencesUtils.getString(this, BleParams.PAIRED_BLE_ADRESS_TAG, "");
        CompanionDeviceHelper.INSTANCE.handleAssociationResult(resultCode, deviceMac);
    }
    private List<String> getConnectPermission() {
        List<String> permissionList = new ArrayList<>();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permissionList.add(Manifest.permission.BLUETOOTH_CONNECT);
            permissionList.add(Manifest.permission.BLUETOOTH_SCAN);
        } else {
            permissionList.add(PermissionUtil.FINE_LOCATION);
            permissionList.add(PermissionUtil.COARSE_LOCATION);
        }
        return permissionList;
    }

    private void requestBluetoothPermission(PermissionItemBean item, int position) {
        PermissionUtils.permission(getConnectPermission().toArray(new String[0]))
                .callback((isAllGranted, grantedList, deniedForever, deniedList) -> {
                    if (isAllGranted) {
                        item.setPermissionState(PermissionUtil.hasBlePermission() ? 1 : 0);
                        permissionAdapter.notifyItemChanged(position);
                        return;
                    }
                    if (!deniedForever.isEmpty()) {
                        requestBlePermission(PermissionUtil.RESULT_BLUETOOTH, SetPermissionActivity.this);
                    }
                }).request();
    }

    private void requestBlePermission(int requestCode, SetPermissionActivity activity) {
        CommonMiddleDialog commonMiddleDialog = new CommonMiddleDialog(activity);
        if (BleParams.isSecurityJewlery()) {
            commonMiddleDialog.setSure(R.string.set_open_hint, v -> {
                PermissionUtil.jumpAppDetail(SetPermissionActivity.this, PermissionUtil.RESULT_BLUETOOTH);
                commonMiddleDialog.dismiss();
            });
            commonMiddleDialog.setCanceledOnTouchOutside(false);
            commonMiddleDialog.setMessage(R.string.sercurity_location_request_hint);
            commonMiddleDialog.show();
            return;
        }

        commonMiddleDialog.setSure(R.string.set_hint, v -> {
            activity.startActivity(new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                    Uri.parse("package:" + activity.getPackageName())));
            commonMiddleDialog.dismiss();
        });

        switch (requestCode) {
            case PermissionUtil.RESULT_LOCATION:
                commonMiddleDialog.setMessage(R.string.location_request_hint);
                break;
            case PermissionUtil.RESULT_BLUETOOTH:
                commonMiddleDialog.setMessage(R.string.ble_request_hint);
                break;
            default:
                commonMiddleDialog.setMessage(R.string.permission_request_common_hint);
        }
        commonMiddleDialog.setCancel(R.string.cancel);
        commonMiddleDialog.show();
    }

    /**
     * 显示隐私保护说明弹窗
     */
    private void showPrivacyProtectionDialog() {
        PrivacyProtectionDialog dialog = new PrivacyProtectionDialog(this);
        dialog.show();
    }

    /**
     * 检查是否有精确闹钟权限
     */
    private boolean hasExactAlarmPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            AlarmManager alarmManager = (AlarmManager) getSystemService(ALARM_SERVICE);
            return alarmManager.canScheduleExactAlarms();
        }
        return true; // Android 12以下默认有权限
    }

    /**
     * 请求精确闹钟权限
     */
    private void requestExactAlarmPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            try {
                Intent intent = new Intent(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM);
                intent.setData(Uri.parse("package:" + getPackageName()));
                startActivityForResult(intent, 1001);
            } catch (Exception e) {
                // 如果无法打开精确闹钟设置，打开应用详情页
                try {
                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    intent.setData(Uri.parse("package:" + getPackageName()));
                    startActivity(intent);
                } catch (Exception e2) {
                    e2.printStackTrace();
                }
            }
        }
    }

    public class PermissionAdapter extends BaseQuickAdapter<PermissionItemBean, BaseViewHolder> {

        public PermissionAdapter() {
            super(R.layout.item_permission);
        }

        @Override
        protected void convert(BaseViewHolder helper, PermissionItemBean item) {
            if (item != null) {
                // 设置权限名称和描述
                helper.setText(R.id.permission_title_tv, item.getPermissionName());
                helper.setText(R.id.permission_content_tv, item.getPermissionDesc());

                // 根据权限状态设置不同的UI
                if (item.getPermissionState() == 0) {
                    helper.setBackgroundRes(R.id.permission_set_cl, R.drawable.item_permisson_seted)
                            .setText(R.id.permission_set_tv, mContext.getString(R.string.page_permission_to_set))
                            .setTextColor(R.id.permission_set_tv, ContextCompat.getColor(mContext, R.color.white))
                            .setImageResource(R.id.nav, R.drawable.item_permisson_nav_black);
                } else if (item.getPermissionState() == 1) {
                    helper.setText(R.id.permission_set_tv, mContext.getString(R.string.page_permission_seted))
                            .setTextColor(R.id.permission_set_tv, Color.parseColor("#ff7a7a7a"))
                            .setBackgroundRes(R.id.permission_set_cl, R.drawable.item_permisson_set)
                            .setVisible(R.id.nav, false);
                } else {
                    helper.setText(R.id.permission_set_tv, mContext.getString(R.string.page_permission_check))
                            .setTextColor(R.id.permission_set_tv, ContextCompat.getColor(mContext, R.color.white))
                            .setBackgroundRes(R.id.permission_set_cl, R.drawable.item_permisson_seted)
                            .setImageResource(R.id.nav, R.drawable.item_permisson_nav_gray);
                }
            }

            // 添加点击事件
            helper.addOnClickListener(R.id.permission_set_cl);
        }
    }

}
