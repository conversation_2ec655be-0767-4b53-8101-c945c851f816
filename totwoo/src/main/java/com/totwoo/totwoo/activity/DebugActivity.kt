package com.totwoo.totwoo.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.bluetooth.BluetoothDevice
import android.companion.AssociationRequest
import android.companion.BluetoothDeviceFilter
import android.os.Build
import android.os.Bundle
import android.text.method.ScrollingMovementMethod
import com.totwoo.library.util.LogUtils
import com.totwoo.library.util.ext.click
import com.totwoo.totwoo.R
import com.totwoo.totwoo.ble.BleParams
import com.totwoo.totwoo.databinding.ActivityDebugBinding
import com.totwoo.totwoo.keepalive.companion.CompanionDeviceHelper
import com.totwoo.totwoo.utils.CommonUtils
import com.totwoo.totwoo.utils.PreferencesUtils
import java.text.SimpleDateFormat
import java.util.*
import android.companion.CompanionDeviceManager
import android.content.Context
import android.content.Intent
import android.content.IntentSender
import androidx.annotation.RequiresApi

/**
 * @des: 调试页面，包含 CompanionDeviceHelper 调试功能
 * <AUTHOR>
 * @date 2024/12/20 11:42
 */
@RequiresApi(Build.VERSION_CODES.O)
class DebugActivity : BaseActivity() {
    private var binding: ActivityDebugBinding? = null
    private val dateFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())


    private val deviceManager: CompanionDeviceManager by lazy {
        getSystemService(Context.COMPANION_DEVICE_SERVICE) as CompanionDeviceManager
    }


    @RequiresApi(Build.VERSION_CODES.O)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDebugBinding.inflate(layoutInflater)
        setContentView(binding?.root)

        initViews()
        initCompanionDeviceHelper()

//        autoPaired()

    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun autoPaired() {
        val deviceMac = PreferencesUtils.getString(this, BleParams.PAIRED_BLE_ADRESS_TAG, "")

        val deviceFilter: BluetoothDeviceFilter = BluetoothDeviceFilter.Builder()
    //            .setNamePattern(Pattern.compile("My device"))
    //            .addServiceUuid(ParcelUuid(UUID(0x123abcL, -1L)), null)
//            .setAddress("F1:CC:69:60:A2:6F")
            .build()

        // The argument provided in setSingleDevice() determines whether a single
        // device name or a list of them appears.
        val pairingRequest: AssociationRequest = AssociationRequest.Builder()
            .addDeviceFilter(deviceFilter)
            .setSingleDevice(false)
            .build()

        // When the app tries to pair with a Bluetooth device, show the
        // corresponding dialog box to the user.
        deviceManager.associate(
            pairingRequest,
            object : CompanionDeviceManager.Callback() {

                override fun onDeviceFound(chooserLauncher: IntentSender) {
                    startIntentSenderForResult(
                        chooserLauncher,
                        100, null, 0, 0, 0
                    )
                }

                override fun onFailure(error: CharSequence?) {
                    // Handle the failure.
                }
            }, null
        )
    }

    @SuppressLint("MissingPermission")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            100 -> when(resultCode) {
                Activity.RESULT_OK -> {
                    // The user chose to pair the app with a Bluetooth device.
                    val deviceToPair: BluetoothDevice? =
                        data?.getParcelableExtra(CompanionDeviceManager.EXTRA_DEVICE)
                    deviceToPair?.let { device ->
                        device.createBond()
                        // Maintain continuous interaction with a paired device.
                    }
                }
            }
            else -> super.onActivityResult(requestCode, resultCode, data)
        }
    }

    private fun initViews() {
        binding?.apply {
            // 设置日志文本框可滚动
            tvDebugLog.movementMethod = ScrollingMovementMethod()

            // 原有的分享功能
            btnShare.click {
                CommonUtils.shareXLog(this@DebugActivity)
            }

            // CompanionDeviceHelper 调试按钮
            btnAutoAssociate.click {
                testAutoAssociate()
            }

            btnRequestNotificationAccess.click {
                testRequestNotificationAccess()
            }

            btnStartObserving.click {
                testStartObserving()
            }

            btnStopObserving.click {
                testStopObserving()
            }

            btnDisassociate.click {
                testDisassociate()
            }

            btnCheckStatus.click {
                checkAssociationStatus()
            }
        }
    }

    private fun initCompanionDeviceHelper() {
        try {
            CompanionDeviceHelper.init()
            appendLog("✅ CompanionDeviceHelper 初始化成功")
        } catch (e: Exception) {
            appendLog("❌ CompanionDeviceHelper 初始化失败: ${e.message}")
        }
    }



    /**
     * 测试自动关联已配对设备
     */
    private fun testAutoAssociate() {

//        autoPaired()
//        appendLog("🔄 开始测试自动关联已配对设备...")
//
        // 获取当前配对的设备信息
        val deviceMac = PreferencesUtils.getString(this, BleParams.PAIRED_BLE_ADRESS_TAG, "")
        val deviceName = PreferencesUtils.getString(this, BleParams.PAIRED_JEWELRY_NAME_TAG, "")

        if (deviceMac.isEmpty()) {
            appendLog("❌ 未找到已配对的设备MAC地址")
            return
        }

        appendLog("📱 设备信息: $deviceName ($deviceMac)")

        CompanionDeviceHelper.autoAssociateWithPairedDevice(
            activity = this,
            deviceMac = deviceMac,
            callback = object : CompanionDeviceHelper.CompanionDeviceCallback {
                override fun onDeviceAssociated(deviceMac: String) {
                    appendLog("✅ 设备关联成功: $deviceMac")
                }

                override fun onError(error: String) {
                    appendLog("❌ 设备关联失败: $error")
                }
            }
        )
    }

    /**
     * 测试申请后台运行权限
     */
    private fun testRequestNotificationAccess() {
        appendLog("🔄 开始测试申请后台运行权限...")

        try {
            CompanionDeviceHelper.requestNotificationAccess(this)
            appendLog("✅ 后台运行权限申请已发起")
        } catch (e: Exception) {
            appendLog("❌ 申请后台运行权限失败: ${e.message}")
        }
    }

    /**
     * 测试开始设备存在监听
     */
    private fun testStartObserving() {
        appendLog("🔄 开始测试设备存在监听...")

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            appendLog("❌ 设备监听功能需要 Android 12+ (当前: Android ${Build.VERSION.SDK_INT})")
            return
        }

        try {
            CompanionDeviceHelper.startObservingDevicePresence()
        } catch (e: Exception) {
            appendLog("❌ 启动设备监听失败: ${e.message}")
        }
    }

    /**
     * 测试停止设备监听
     */
    private fun testStopObserving() {

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            appendLog("❌ 设备监听功能需要 Android 12+ (当前: Android ${Build.VERSION.SDK_INT})")
            return
        }

        try {
            CompanionDeviceHelper.stopObservingDevicePresence()
        } catch (e: Exception) {
            appendLog("❌ 停止设备监听失败: ${e.message}")
        }
    }

    /**
     * 测试解除设备关联
     */
    private fun testDisassociate() {

        try {
            CompanionDeviceHelper.disassociateDevice()
        } catch (e: Exception) {
            appendLog("❌ 解除设备关联失败: ${e.message}")
        }
    }

    /**
     * 检查关联状态
     */
    private fun checkAssociationStatus() {
        appendLog("🔄 检查设备关联状态...")

        try {
            val isSupported = CompanionDeviceHelper.isCompanionDeviceSupported()
            appendLog("📋 系统支持状态: ${if (isSupported) "支持" else "不支持"} (需要 Android 8+)")
            appendLog("📋 当前 Android 版本: API ${Build.VERSION.SDK_INT} (Android ${getAndroidVersionName()})")

            if (isSupported) {
                val deviceMac = PreferencesUtils.getString(this, BleParams.PAIRED_BLE_ADRESS_TAG, "")

                appendLog("📋 设备MAC地址: $deviceMac")
                if (deviceMac.isNotEmpty()) {
                    // 检查特定设备是否已关联
                    val isSpecificDeviceAssociated = CompanionDeviceHelper.isDeviceAssociated(deviceMac)
                    appendLog("📋 当前设备是否已关联: ${if (isSpecificDeviceAssociated) "是" else "否"}")
                } else {
                    appendLog("❌ 没有可关联的设备")
                }
            }
        } catch (e: Exception) {
            appendLog("❌ 检查状态失败: ${e.message}")
        }
    }

    /**
     * 获取Android版本名称
     */
    private fun getAndroidVersionName(): String {
        return when (Build.VERSION.SDK_INT) {
            26 -> "8.0"
            27 -> "8.1"
            28 -> "9"
            29 -> "10"
            30 -> "11"
            31 -> "12"
            32 -> "12L"
            33 -> "13"
            34 -> "14"
            35 -> "15"
            else -> "Unknown"
        }
    }

    /**
     * 添加日志到界面
     */
    private fun appendLog(message: String) {
        val timestamp = dateFormat.format(Date())
        val logMessage = "[$timestamp] $message"

        LogUtils.d("DebugActivity", logMessage)

        runOnUiThread {
            binding?.tvDebugLog?.apply {
                val currentText = text.toString()
                val newText = if (currentText == "调试日志将显示在这里...") {
                    logMessage
                } else {
                    "$currentText\n$logMessage"
                }
                text = newText

                // 滚动到底部
                post {
                    val scrollAmount = layout?.getLineTop(lineCount) ?: 0
                    if (scrollAmount > height) {
                        scrollTo(0, scrollAmount - height)
                    }
                }
            }
        }
    }

    override fun initTopBar() {
        super.initTopBar()
        setTopBackIcon(R.drawable.back_icon_black)
        setTopTitle("调试")
        setTopLeftOnclik { finish() }
        setSpinState(false)
    }



    override fun onDestroy() {
        super.onDestroy()
        binding = null
    }
}