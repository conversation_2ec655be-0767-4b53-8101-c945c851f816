package com.totwoo.totwoo.activity.wish;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.databinding.ActivityMediaPrewBinding;
import com.totwoo.totwoo.record.PreviewConfig;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.LoadControl;
import com.google.android.exoplayer2.DefaultLoadControl;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.ui.PlayerView;

import java.io.IOException;

/**
 * 多媒体预览: 支持图片, 视频(封面图)
 */
public class MediaPreviewActivity extends BaseActivity {
    private static final String TAG = "MediaPreviewActivity";

    private PreviewConfig config;
    private ExoPlayer exoPlayer;
    private PlayerView playerView;
    private boolean isVideoInitialized = false;
    private ActivityMediaPrewBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityMediaPrewBinding.inflate(LayoutInflater.from(this));
        setContentView(binding.getRoot());
        CommonUtils.setStateBar(this, true);

        config = (PreviewConfig) getIntent().getSerializableExtra(PreviewConfig.EXTRA_PREVIEW_CONFIG_TAG);

        if (config == null) {
            ToastUtils.showLong(this, R.string.data_error);
            finish();
            return;
        }

        try {
            initData();
        } catch (Exception e) {
            e.printStackTrace();
            ToastUtils.showLong(this, R.string.data_error);
        }
    }

    private void initData() throws IOException {
        Log.d(TAG, "initData with: " + config);

        String imageUrl = config.getCoverPath() == null ? config.getVideoPath() : config.getCoverPath();
        RequestOptions requestOptions = new RequestOptions();
        if (imageUrl != null && imageUrl.startsWith("/")) {
            requestOptions = requestOptions.skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE);
        }

        Glide.with(MediaPreviewActivity.this)
                .asBitmap()
                .load(HttpHelper.getRealImageUrl(imageUrl))
                .apply(requestOptions)
                .addListener(new RequestListener<Bitmap>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Bitmap> target, boolean isFirstResource) {
                        // 等预览图加载完毕后, 在加载视频, 否则 cpu 会被占用, 无法显示预览图
                        if (!TextUtils.isEmpty(config.getVideoPath())) {
                            initExoPlayer();
                        }
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(Bitmap resource, Object model, Target<Bitmap> target, DataSource dataSource, boolean isFirstResource) {
                        binding.mediaCoverView.setImageBitmap(resource);
                        if (!TextUtils.isEmpty(config.getVideoPath())) {
                            binding.mediaProgressbar.setVisibility(View.VISIBLE);
                            binding.mediaCoverView.post(MediaPreviewActivity.this::initExoPlayer);
                        }
                        return true;
                    }
                })
                .submit();

        if (!TextUtils.isEmpty(config.getInfo())) {
            binding.mediaInfoTv.setVisibility(View.VISIBLE);
            binding.mediaInfoTv.setText(config.getInfo());
            binding.mediaInfoTv.setOnClickListener(v -> {
                binding.mediaInfoTv.toggle(true);
            });
        }

        if (!TextUtils.isEmpty(config.getVideoPath())) {
            initExoPlayer();
        } else {
            binding.mediaProgressbar.setVisibility(View.GONE);
        }
    }

    private void initExoPlayer() {
        Log.d(TAG, "Initializing ExoPlayer for video: " + config.getVideoPath());

        // 创建 LoadControl 配置，优化网络播放
        LoadControl loadControl = new DefaultLoadControl.Builder()
                .setBufferDurationsMs(
                        15000,  // 最小缓冲时间 15秒
                        50000,  // 最大缓冲时间 50秒
                        1500,   // 播放缓冲时间 1.5秒
                        5000    // 重新缓冲时间 5秒
                )
                .build();

        // 创建 ExoPlayer 实例
        exoPlayer = new ExoPlayer.Builder(this)
                .setLoadControl(loadControl)
                .build();

        // 获取 PlayerView 并设置播放器
        playerView = binding.mediaVideoView;
        playerView.setPlayer(exoPlayer);

        // 确保控制器可见
        playerView.setUseController(true);
        playerView.setControllerShowTimeoutMs(3000); // 控制器3秒后隐藏
        Log.d(TAG, "PlayerView configured with controller");

        // 创建媒体项
        String videoUrl = HttpHelper.getRealImageUrl(config.getVideoPath());
        Log.d(TAG, "Video URL: " + videoUrl);
        MediaItem mediaItem = MediaItem.fromUri(videoUrl);
        exoPlayer.setMediaItem(mediaItem);
        Log.d(TAG, "Media item set");

        // 设置循环播放
        exoPlayer.setRepeatMode(config.isLoopPlay() ? Player.REPEAT_MODE_ONE : Player.REPEAT_MODE_OFF);

        // 添加播放器监听器
        exoPlayer.addListener(new Player.Listener() {
            @Override
            public void onPlaybackStateChanged(int playbackState) {
                String stateString = "";
                switch (playbackState) {
                    case Player.STATE_IDLE: stateString = "IDLE"; break;
                    case Player.STATE_BUFFERING: stateString = "BUFFERING"; break;
                    case Player.STATE_READY: stateString = "READY"; break;
                    case Player.STATE_ENDED: stateString = "ENDED"; break;
                }
                Log.d(TAG, "Playback state changed: " + stateString + " (" + playbackState + ")");
                Log.d(TAG, "Is playing: " + exoPlayer.isPlaying());
                Log.d(TAG, "Play when ready: " + exoPlayer.getPlayWhenReady());

                switch (playbackState) {
                    case Player.STATE_READY:
                        // 视频准备就绪
                        isVideoInitialized = true;
                        binding.mediaProgressbar.setVisibility(View.GONE);
                        binding.mediaCoverView.setVisibility(View.GONE);
                        playerView.setVisibility(View.VISIBLE);
                        Log.d(TAG, "Video ready to play");
                        break;
                    case Player.STATE_BUFFERING:
                        // 缓冲中
                        binding.mediaProgressbar.setVisibility(View.VISIBLE);
                        Log.d(TAG, "Video buffering");
                        break;
                    case Player.STATE_ENDED:
                        // 播放结束
                        Log.d(TAG, "Video playback ended");
                        break;
                    case Player.STATE_IDLE:
                        // 空闲状态
                        Log.d(TAG, "Video player idle");
                        break;
                }
            }

            @Override
            public void onPlayerError(PlaybackException error) {
                Log.e(TAG, "ExoPlayer error: " + error.getMessage());
                binding.mediaProgressbar.setVisibility(View.GONE);
                ToastUtils.showLong(MediaPreviewActivity.this, R.string.video_play_error);

                // 可以尝试重新加载
                if (!isFinishing()) {
                    retryVideoLoad();
                }
            }

            @Override
            public void onIsPlayingChanged(boolean isPlaying) {
                Log.d(TAG, "Is playing changed: " + isPlaying);
            }
        });



        // 添加调试按钮点击事件
        binding.debugPlayButton.setOnClickListener(v -> {
            Log.d(TAG, "Debug button clicked");
            Log.d(TAG, "Current state - Playing: " + exoPlayer.isPlaying() + ", PlayWhenReady: " + exoPlayer.getPlayWhenReady());
            if (exoPlayer.isPlaying()) {
                exoPlayer.pause();
                Log.d(TAG, "Paused via debug button");
            } else {
                exoPlayer.play();
                Log.d(TAG, "Started via debug button");
            }
        });

        // 准备播放器并自动开始播放
        exoPlayer.prepare();
        exoPlayer.setPlayWhenReady(true);
        Log.d(TAG, "ExoPlayer prepared and set to play when ready");
    }

    private void retryVideoLoad() {
        Log.d(TAG, "Retrying video load...");
        if (exoPlayer != null) {
            exoPlayer.stop();
            exoPlayer.prepare();
            exoPlayer.setPlayWhenReady(true);
        }
    }


    @Override
    protected void onResume() {
        super.onResume();
        Log.d(TAG, "onResume");
        if (exoPlayer != null && isVideoInitialized) {
            exoPlayer.setPlayWhenReady(true);
            Log.d(TAG, "ExoPlayer resumed");
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        Log.d(TAG, "onPause");
        if (exoPlayer != null) {
            exoPlayer.setPlayWhenReady(false);
            Log.d(TAG, "ExoPlayer paused");
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy");
        // 释放 ExoPlayer 资源
        if (exoPlayer != null) {
            exoPlayer.stop();
            exoPlayer.release();
            exoPlayer = null;
            Log.d(TAG, "ExoPlayer resources released");
        }
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_white);
        setTopLeftOnclik(v -> finish());
        setTopbarBackground(R.color.black);

        if (config.getTitleId() != 0) {
            setTopTitle(config.getTitleId());
            setTopTitleColor(0xffffffff);
        }

        if (config.getMenuListener() != null) {
            if (config.getMenuIcon() != 0) {
                setTopRightIcon(config.getMenuIcon());
            } else {
                setTopRightString(config.getMenuTextId());
            }
            setTopRightOnClick(v -> config.getMenuListener().onClick(this));
        }
    }
}
