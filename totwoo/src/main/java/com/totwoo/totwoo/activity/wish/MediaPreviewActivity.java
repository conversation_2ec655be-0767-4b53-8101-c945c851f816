package com.totwoo.totwoo.activity.wish;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.activity.BaseActivity;
import com.totwoo.totwoo.databinding.ActivityMediaPrewBinding;
import com.totwoo.totwoo.record.PreviewConfig;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.ToastUtils;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.LoadControl;
import com.google.android.exoplayer2.DefaultLoadControl;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.ui.PlayerView;

import java.io.IOException;

/**
 * 多媒体预览: 支持图片, 视频(封面图)
 */
public class MediaPreviewActivity extends BaseActivity {
    private static final String TAG = "MediaPreviewActivity";

    private PreviewConfig config;
    private ExoPlayer exoPlayer;
    private PlayerView playerView;
    private boolean isVideoInitialized = false;
    private ActivityMediaPrewBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityMediaPrewBinding.inflate(LayoutInflater.from(this));
        setContentView(binding.getRoot());
        CommonUtils.setStateBar(this, true);

        config = (PreviewConfig) getIntent().getSerializableExtra(PreviewConfig.EXTRA_PREVIEW_CONFIG_TAG);

        if (config == null) {
            ToastUtils.showLong(this, R.string.data_error);
            finish();
            return;
        }

        try {
            initData();
        } catch (Exception e) {
            e.printStackTrace();
            ToastUtils.showLong(this, R.string.data_error);
        }
    }

    private void initData() throws IOException {
        Log.d(TAG, "initData with: " + config);

        String imageUrl = config.getCoverPath() == null ? config.getVideoPath() : config.getCoverPath();
        RequestOptions requestOptions = new RequestOptions();
        if (imageUrl != null && imageUrl.startsWith("/")) {
            requestOptions = requestOptions.skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE);
        }

        Glide.with(MediaPreviewActivity.this)
                .asBitmap()
                .load(HttpHelper.getRealImageUrl(imageUrl))
                .apply(requestOptions)
                .addListener(new RequestListener<Bitmap>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Bitmap> target, boolean isFirstResource) {
                        // 等预览图加载完毕后, 在加载视频, 否则 cpu 会被占用, 无法显示预览图
                        if (!TextUtils.isEmpty(config.getVideoPath())) {
                            initExoPlayer();
                        }
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(Bitmap resource, Object model, Target<Bitmap> target, DataSource dataSource, boolean isFirstResource) {
                        binding.mediaCoverView.setImageBitmap(resource);
                        if (!TextUtils.isEmpty(config.getVideoPath())) {
                            binding.mediaProgressbar.setVisibility(View.VISIBLE);
                            binding.mediaCoverView.post(MediaPreviewActivity.this::initExoPlayer);
                        }
                        return true;
                    }
                })
                .submit();

        if (!TextUtils.isEmpty(config.getInfo())) {
            binding.mediaInfoTv.setVisibility(View.VISIBLE);
            binding.mediaInfoTv.setText(config.getInfo());
            binding.mediaInfoTv.setOnClickListener(v -> {
                binding.mediaInfoTv.toggle(true);
            });
        }

        if (!TextUtils.isEmpty(config.getVideoPath())) {
            initExoPlayer();
        } else {
            binding.mediaProgressbar.setVisibility(View.GONE);
        }
    }

    private void initExoPlayer() {
        // 创建 LoadControl 配置，优化网络播放
        LoadControl loadControl = new DefaultLoadControl.Builder()
                .setBufferDurationsMs(
                        15000,  // 最小缓冲时间 15秒
                        50000,  // 最大缓冲时间 50秒
                        1500,   // 播放缓冲时间 1.5秒
                        5000    // 重新缓冲时间 5秒
                )
                .build();

        // 创建 ExoPlayer 实例
        exoPlayer = new ExoPlayer.Builder(this)
                .setLoadControl(loadControl)
                .build();

        // 获取 PlayerView 并设置播放器
        playerView = binding.mediaVideoView;
        playerView.setPlayer(exoPlayer);
        playerView.setUseController(true);

        // 创建媒体项
        String videoUrl = HttpHelper.getRealImageUrl(config.getVideoPath());
        MediaItem mediaItem = MediaItem.fromUri(videoUrl);
        exoPlayer.setMediaItem(mediaItem);

        // 设置循环播放
        exoPlayer.setRepeatMode(config.isLoopPlay() ? Player.REPEAT_MODE_ONE : Player.REPEAT_MODE_OFF);

        // 添加播放器监听器
        exoPlayer.addListener(new Player.Listener() {
            @Override
            public void onPlaybackStateChanged(int playbackState) {
                switch (playbackState) {
                    case Player.STATE_READY:
                        // 视频准备就绪
                        isVideoInitialized = true;
                        binding.mediaProgressbar.setVisibility(View.GONE);
                        binding.mediaCoverView.setVisibility(View.GONE);
                        playerView.setVisibility(View.VISIBLE);
                        break;
                    case Player.STATE_BUFFERING:
                        // 缓冲中
                        binding.mediaProgressbar.setVisibility(View.VISIBLE);
                        break;
                }
            }

            @Override
            public void onPlayerError(PlaybackException error) {
                binding.mediaProgressbar.setVisibility(View.GONE);
                ToastUtils.showLong(MediaPreviewActivity.this, R.string.video_play_error);

                // 尝试重新加载
                if (!isFinishing()) {
                    retryVideoLoad();
                }
            }
        });

        // 准备播放器并自动开始播放
        exoPlayer.prepare();
        exoPlayer.setPlayWhenReady(true);
    }

    private void retryVideoLoad() {
        if (exoPlayer != null) {
            exoPlayer.stop();
            exoPlayer.prepare();
            exoPlayer.setPlayWhenReady(true);
        }
    }


    @Override
    protected void onResume() {
        super.onResume();
        if (exoPlayer != null && isVideoInitialized) {
            exoPlayer.setPlayWhenReady(true);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (exoPlayer != null) {
            exoPlayer.setPlayWhenReady(false);
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        // 在 onStop 时暂停播放，确保后台不继续播放
        if (exoPlayer != null) {
            exoPlayer.pause();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 释放 ExoPlayer 资源
        releasePlayer();
    }

    @Override
    public void onBackPressed() {
        // 确保返回时释放播放器资源
        releasePlayer();
        super.onBackPressed();
    }

    private void releasePlayer() {
        if (exoPlayer != null) {
            exoPlayer.stop();
            exoPlayer.clearMediaItems();
            exoPlayer.release();
            exoPlayer = null;
        }
        if (playerView != null) {
            playerView.setPlayer(null);
        }
    }

    @Override
    protected void initTopBar() {
        setTopBackIcon(R.drawable.back_icon_white);
        setTopLeftOnclik(v -> {
            releasePlayer();
            finish();
        });
        setTopbarBackground(R.color.black);

        if (config.getTitleId() != 0) {
            setTopTitle(config.getTitleId());
            setTopTitleColor(0xffffffff);
        }

        if (config.getMenuListener() != null) {
            if (config.getMenuIcon() != 0) {
                setTopRightIcon(config.getMenuIcon());
            } else {
                setTopRightString(config.getMenuTextId());
            }
            setTopRightOnClick(v -> config.getMenuListener().onClick(this));
        }
    }
}
