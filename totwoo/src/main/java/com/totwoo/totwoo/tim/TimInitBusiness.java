package com.totwoo.totwoo.tim;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.res.Resources;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.tencent.imsdk.v2.V2TIMConversation;
import com.tencent.imsdk.v2.V2TIMConversationListener;
import com.tencent.imsdk.v2.V2TIMManager;
import com.tencent.imsdk.v2.V2TIMMessage;
import com.tencent.imsdk.v2.V2TIMSimpleMsgListener;
import com.tencent.imsdk.v2.V2TIMUserInfo;
import com.tencent.imsdk.v2.V2TIMValueCallback;
import com.tencent.qcloud.tim.tuiofflinepush.utils.BrandUtil;
import com.tencent.qcloud.tuicore.TUIConstants;
import com.tencent.qcloud.tuicore.TUICore;
import com.tencent.qcloud.tuicore.TUILogin;
import com.tencent.qcloud.tuicore.TUIThemeManager;
import com.tencent.qcloud.tuicore.component.interfaces.IUIKitCallback;
import com.tencent.qcloud.tuicore.interfaces.ITUINotification;
import com.tencent.qcloud.tuicore.interfaces.TUICallback;
import com.tencent.qcloud.tuicore.interfaces.TUILoginListener;
import com.tencent.qcloud.tuicore.util.ErrorMessageConverter;
import com.tencent.qcloud.tuicore.util.PermissionRequester;
import com.tencent.qcloud.tuicore.util.ScreenUtil;
import com.tencent.qcloud.tuikit.tuichat.TUIChatService;
import com.tencent.qcloud.tuikit.tuichat.component.face.CustomFace;
import com.tencent.qcloud.tuikit.tuichat.component.face.CustomFaceConfig;
import com.tencent.qcloud.tuikit.tuichat.component.face.CustomFaceGroup;
import com.tencent.qcloud.tuikit.tuichat.component.face.FaceManager;
import com.tencent.qcloud.tuikit.tuicontact.presenter.FriendProfilePresenter;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.WelcomeActivity;
import com.totwoo.totwoo.bean.TimSig;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Locale;
import java.util.Map;

import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;


/**
 * 初始化
 * 包括imsdk等
 */
public class TimInitBusiness {

    private static final String TAG = TimInitBusiness.class.getSimpleName();

    /**
     * 颜文字表情包,组 ID;
     */
    public static final int TOTWOO_FACE_GROUP_WORD = 100001;

    private static V2TIMConversationListener v2TIMConversationListener;
    private static long unReadMsgCount = 0;
    private static TUICallback loginCallback;
    private static FriendProfilePresenter profilePresenter;

    /**
     * IM SDK 前置处理, SDK 实际的 init 再 login 内部进行
     */
    public static void init(Context context, int logLevel) {
        initBuildInformation();

        initCustomFace(context);

        TUIThemeManager.addLanguage("fr", Locale.FRANCE);
        TUIThemeManager.addLanguage("de", Locale.GERMANY);
        TUIThemeManager.addLanguage("it", Locale.ITALY);
        TUIThemeManager.addLanguage("ja", Locale.JAPAN);
        TUIThemeManager.addLanguage("ru", new Locale("ru", "RU"));
        // 监听登录失效, 重新登录
        TUILogin.addLoginListener(new TUILoginListener() {
            @Override
            public void onConnecting() {
                super.onConnecting();
                LogUtils.d(TAG, "onConnecting");
            }

            @Override
            public void onConnectSuccess() {
                super.onConnectSuccess();
                LogUtils.d(TAG, "onConnectSuccess");
            }

            @Override
            public void onConnectFailed(int code, String error) {
                super.onConnectFailed(code, error);
                LogUtils.e(TAG, "onConnectFailed");
            }

            @Override
            public void onKickedOffline() {
                super.onKickedOffline();
                LogUtils.e(TAG, "onKickedOffline");

                // 单点登录被踢出
                login();
            }

            @Override
            public void onUserSigExpired() {
                super.onUserSigExpired();
                LogUtils.e(TAG, "onUserSigExpired");

                // token失效
                login();
            }
        });

//        TUIThemeManager.setWebViewLanguage(context);

        setPermissionRequestContent(context);

        // 需要尽早处理, 否则会导致收不到第一次点击回调事件
//        initOfflinePushConfigs(context);

        v2TIMConversationListener = new V2TIMConversationListener() {
            @Override
            public void onTotalUnreadMessageCountChanged(long totalUnreadCount) {
                super.onTotalUnreadMessageCountChanged(totalUnreadCount);

                if (totalUnreadCount > unReadMsgCount) {
                    com.etone.framework.event.EventBus.onPostReceived(S.E.E_RECEIVED_IM_MESSAGE, null);
                }
                unReadMsgCount = totalUnreadCount;
            }
        };
        V2TIMManager.getConversationManager().addConversationListener(v2TIMConversationListener);
    }

    private static void initCustomFace(Context context) {
        CustomFaceConfig faceConfig = TUIChatService.getChatConfig().getCustomFaceConfig();
        if (faceConfig == null) {
            faceConfig = new CustomFaceConfig();
            TUIChatService.getChatConfig().setCustomFaceConfig(faceConfig);
        }

        boolean hasWord = false;
        if (faceConfig.getFaceGroups() != null) {
            for (CustomFaceGroup faceGroup : faceConfig.getFaceGroups()) {
                if (faceGroup.getFaceGroupId() == TOTWOO_FACE_GROUP_WORD) {
                    hasWord = true;
                }
            }
        }

        if (!hasWord) {
            CustomFaceGroup faceWord = new CustomFaceGroup();
            faceWord.setFaceGroupId(TOTWOO_FACE_GROUP_WORD);
            faceWord.setFaceIconName("The text expression");
            faceWord.setFaceIconPath("im_face/face_symbol.png");
            faceWord.setPageRowCount(4);
            faceWord.setPageColumnCount(5);

            try {
                String[] fs = context.getAssets().list("im_face/symbol");
                if (fs != null) {
                    for (String s : fs) {
                        CustomFace face = new CustomFace("im_face/symbol/" + s, s.substring(0, s.lastIndexOf(".")), ScreenUtil.dip2px(62), ScreenUtil.dip2px(24));
                        faceWord.addCustomFace(face);
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            faceConfig.addFaceGroup(faceWord);
            FaceManager.loadFaceFiles();
        }
    }

    /**
     * 注册推送通知点击后的跳转.
     *
     * @param context
     */
    public static void initOfflinePushConfigs(Context context) {
        // 注册通知跳转回调
        TUICore.registerEvent(TUIConstants.TUIOfflinePush.EVENT_NOTIFY,
                TUIConstants.TUIOfflinePush.EVENT_NOTIFY_NOTIFICATION, new ITUINotification() {
                    @Override
                    public void onNotifyEvent(String key, String subKey, Map<String, Object> param) {
                        Log.d(TAG, "onNotifyEvent key = " + key + "subKey = " + subKey);
                        if (TUIConstants.TUIOfflinePush.EVENT_NOTIFY.equals(key)) {
                            if (TUIConstants.TUIOfflinePush.EVENT_NOTIFY_NOTIFICATION.equals(subKey)) {
                                if (param != null) {
                                    String extString = (String) param.get(TUIConstants.TUIOfflinePush.NOTIFICATION_EXT_KEY);
                                    pushNotifyOpen(context, extString);
                                }
                            }
                        }
                    }
                });

        if (BrandUtil.isBrandXiaoMi() || BrandUtil.isBrandVivo() || BrandUtil.isBrandOppo() || BrandUtil.isGoogleServiceSupport(ToTwooApplication.baseContext)) {
        } else {
            // 无离线推送的场景
            V2TIMManager.getInstance().addSimpleMsgListener(new V2TIMSimpleMsgListener() {
                @Override
                public void onRecvC2CTextMessage(String msgID, V2TIMUserInfo sender, String text) {
                    super.onRecvC2CTextMessage(msgID, sender, text);
                    LogUtils.d(TAG, "onRecvC2CTextMessage() called with: msgID = [" + msgID + "], sender = [" + sender + "], text = [" + text + "]");
                    tryShowMessageNotification(context, msgID, sender.getNickName(), text);
                }

                @Override
                public void onRecvC2CCustomMessage(String msgID, V2TIMUserInfo sender, byte[] customData) {
                    super.onRecvC2CCustomMessage(msgID, sender, customData);
                    LogUtils.d(TAG, "onRecvC2CCustomMessage() called with: msgID = [" + msgID + "], sender = [" + sender + "], customData = [" + customData + "]");

                    V2TIMMessage v2TIMMessage = V2TIMManager.getMessageManager().createCustomMessage(customData);
                    if (v2TIMMessage != null && v2TIMMessage.getCustomElem().getDescription() != null) {
                        tryShowMessageNotification(context, msgID, sender.getNickName(), v2TIMMessage.getCustomElem().getDescription());
                    }

                }
            });
        }
    }

    private static void tryShowMessageNotification(Context context, String msgId, String nickName, String text) {
        final ArrayList<String> userIdList = new ArrayList<>();

        userIdList.add(ToTwooApplication.otherPhone);

        if (profilePresenter == null) {
            profilePresenter = new FriendProfilePresenter();
        }
        // 获取对应应用的免打扰设置
        profilePresenter.getC2CReceiveMessageOpt(userIdList, new IUIKitCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean data) {
                if (data != null && !data && !ToTwooApplication.isForeground) {
                    NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
                    String channelId = "IM_CHAT";
                    NotificationChannel mChannel;
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                        mChannel = new NotificationChannel(channelId, "totwoo", BrandUtil.isBrandHuawei() ? NotificationManager.IMPORTANCE_DEFAULT : NotificationManager.IMPORTANCE_HIGH);
                        notificationManager.createNotificationChannel(mChannel);
                    }

                    NotificationCompat.Builder builder = new NotificationCompat.Builder(context, channelId);
                    builder.setContentTitle(nickName);
                    builder.setContentText(text);
                    builder.setSmallIcon(R.drawable.ic_launcher_small);
                    // 定义消息类别, 适配各个系统厂商对于消息类别的限制
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        builder.setCategory(Notification.CATEGORY_MESSAGE);
                    }
                    builder.setLargeIcon(BitmapFactory.decodeResource(context.getResources(), R.mipmap.ic_launcher));

                    Intent intent = new Intent(context, WelcomeActivity.class);
                    intent.putExtra("google.message_id", msgId);

                    PendingIntent pendingIntent = PendingIntent.getActivity(context,
                            36, intent,
                            Apputils.wrapMutablePendingFlag(PendingIntent.FLAG_UPDATE_CURRENT));
                    builder.setContentIntent(pendingIntent);

                    notificationManager.notify(msgId.hashCode(), builder.build());
                }
            }

            @Override
            public void onError(String module, int errCode, String errMsg) {
            }
        });

    }

    private static void pushNotifyOpen(Context context, String extData) {
        // 判断是否从后后台拉起
        // 获取activity任务栈
        LogUtils.d(TAG, "pushNotifyOpen: " + extData + ", isForeground: " + ToTwooApplication.isForeground);
        if (!ToTwooApplication.isForeground) {
            context.startActivity(new Intent(context, WelcomeActivity.class).addFlags(Intent.FLAG_ACTIVITY_NEW_TASK));

            new Handler().postDelayed(() -> pushNotifyOpen(context, extData), 3000);
            return;
        }

        navToChat();
    }

    public static void refreshPermissionRequestContent(Context context) {
        setPermissionRequestContent(context);
    }
    /**
     * 注册权限申请流程
     *
     * @param context
     */
    private static void setPermissionRequestContent(Context context) {
        ApplicationInfo applicationInfo = context.getApplicationInfo();
        Resources resources = context.getResources();
        String appName = resources.getString(applicationInfo.labelRes);

        PermissionRequester.PermissionRequestContent microphoneContent = new PermissionRequester.PermissionRequestContent();
        microphoneContent.setReasonTitle(context.getString(R.string.demo_permission_mic_reason_title, appName));
        microphoneContent.setReason(context.getString(R.string.demo_permission_mic_reason));
        microphoneContent.setIconResId(R.drawable.demo_permission_icon_mic);
        microphoneContent.setDeniedAlert(context.getString(R.string.demo_permission_mic_dialog_alert));
        PermissionRequester.setPermissionRequestContent(PermissionRequester.PermissionConstants.MICROPHONE, microphoneContent);

        PermissionRequester.PermissionRequestContent cameraContent = new PermissionRequester.PermissionRequestContent();
        cameraContent.setReasonTitle(context.getString(R.string.demo_permission_camera_reason_title, appName));
        cameraContent.setReason(context.getString(R.string.demo_permission_camera_reason));
        cameraContent.setIconResId(R.drawable.demo_permission_icon_camera);
        cameraContent.setDeniedAlert(context.getString(R.string.demo_permission_camera_dialog_alert));
        PermissionRequester.setPermissionRequestContent(PermissionRequester.PermissionConstants.CAMERA, cameraContent);
    }

    private static void initBuildInformation() {
        try {
            JSONObject buildInfoJson = new JSONObject();
            buildInfoJson.put("buildBrand", BrandUtil.getBuildBrand());
            buildInfoJson.put("buildManufacturer", BrandUtil.getBuildManufacturer());
            buildInfoJson.put("buildModel", BrandUtil.getBuildModel());
            buildInfoJson.put("buildVersionRelease", BrandUtil.getBuildVersionRelease());
            buildInfoJson.put("buildVersionSDKInt", BrandUtil.getBuildVersionSDKInt());
            // 工信部要求 app 在运行期间只能获取一次设备信息。因此 app 获取设备信息设置给 SDK 后，SDK 使用该值并且不再调用系统接口。
            // The Ministry of Industry and Information Technology requires the app to obtain device information only once
            // during its operation. Therefore, after the app obtains the device information and sets it to the SDK, the SDK
            // uses this value and no longer calls the system interface.
            V2TIMManager.getInstance().callExperimentalAPI("setBuildInfo", buildInfoJson.toString(), new V2TIMValueCallback<Object>() {
                @Override
                public void onSuccess(Object o) {
                    LogUtils.i(TAG, "setBuildInfo success");
                }

                @Override
                public void onError(int code, String desc) {
                    LogUtils.i(TAG, "setBuildInfo code:" + code + " desc:" + ErrorMessageConverter.convertIMError(code, desc));
                }
            });
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 登录之后需要执行的操作
     */
    static void afterLogin() {
        V2TIMManager.getConversationManager().getTotalUnreadMessageCount(new V2TIMValueCallback<Long>() {
            @Override
            public void onSuccess(Long aLong) {
                if (aLong != null && aLong > 0) {
                    unReadMsgCount = aLong;
                    com.etone.framework.event.EventBus.onPostReceived(S.E.E_RECEIVED_IM_MESSAGE, null);
                }
            }

            @Override
            public void onError(int i, String s) {

            }
        });
    }


    public static void navToChat() {

        String chatId = ToTwooApplication.otherPhone;

        if (TextUtils.isEmpty(chatId)) {
            chatId = PreferencesUtils.getString(ToTwooApplication.baseContext, CoupleLogic.PAIRED_PERSON_ID, "");
        }

        navToChat(chatId, PreferencesUtils.getString(ToTwooApplication.baseContext, CoupleLogic.PAIRED_PERSON_NICK_NAME, ""));
    }

    /**
     * 跳转到配对聊天页面
     *
     * @param chatId
     * @param chatName
     */
    public static void navToChat(String chatId, String chatName) {
        if (!ToTwooApplication.owner.isLogin()) {
            return;
        }
        if (!TUILogin.isUserLogined()) {
            login(new TUICallback() {
                @Override
                public void onSuccess() {
                    navToChat(chatId, chatName);
                }

                @Override
                public void onError(int errorCode, String errorMessage) {

                }
            });
            return;
        }

        try {
            TUIThemeManager.getInstance().changeLanguage(ToTwooApplication.baseContext, Apputils.getSystemLanguage(ToTwooApplication.baseContext));
        } catch (Exception e) {
            e.printStackTrace();
        }

        Bundle bundle = new Bundle();
        bundle.putString(TUIConstants.TUIChat.CHAT_ID, chatId);
        bundle.putString(TUIConstants.TUIChat.CHAT_NAME, chatName);
        bundle.putInt(TUIConstants.TUIChat.CHAT_TYPE, V2TIMConversation.V2TIM_C2C);
        TUICore.startActivity(TUIConstants.TUIChat.C2C_CHAT_ACTIVITY_NAME, bundle);
        LogUtils.d(TAG, "navToChat() called with: chatId = [" + chatId + "], chatName = [" + chatName + "] Success.");
    }

    /**
     * 登录 imsdk
     */
    public static void login() {
        login(null);
    }

    /**
     * 登录 imsdk
     */
    public static void login(TUICallback callback) {
        final String identify = ToTwooApplication.owner.getPhone();
        if (identify == null) {
            return;
        }

        loginCallback = new TUICallback() {
            @Override
            public void onSuccess() {
                LogUtils.e(TAG, "tim login success.");
                TimInitBusiness.afterLogin();
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onError(int errorCode, String errorMessage) {
                LogUtils.e(TAG, "tim login error, errorCode: " + errorCode + ", errorMessage: " + errorMessage);
                if (callback != null) {
                    callback.onError(errorCode, errorMessage);
                }
            }
        };

        String userSig = PreferencesUtils.getString(ToTwooApplication.baseContext, CommonArgs.TIM_SIG, "");
        long sigTime = PreferencesUtils.getLong(ToTwooApplication.baseContext, CommonArgs.TIM_SIG_TIME_MILLION, 0);
        if (!TextUtils.isEmpty(userSig) && validTime(sigTime)) {
            TUILogin.login(ToTwooApplication.baseContext.getApplicationContext(), TimConstant.SDK_APPID, identify, userSig, loginCallback);
        } else {
            HttpHelper.commonService.getTimSig()
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeOn(Schedulers.newThread())
                    .subscribe(new Observer<HttpBaseBean<TimSig>>() {
                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {

                        }

                        @Override
                        public void onNext(HttpBaseBean<TimSig> timSigHttpBaseBean) {
                            String sig = timSigHttpBaseBean.getData().getSig();
                            PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.TIM_SIG, sig);
                            PreferencesUtils.put(ToTwooApplication.baseContext, CommonArgs.TIM_SIG_TIME_MILLION, System.currentTimeMillis());
                            TUILogin.login(ToTwooApplication.baseContext, TimConstant.SDK_APPID, identify, sig, loginCallback);
                        }
                    });
        }
    }

    private static boolean validTime(long time) {
        return time + (180 * 24 * 3600 * 100) > System.currentTimeMillis();
    }

    /**
     * 登出 imsdk
     */
    public static void logout() {
        TUILogin.logout(new TUICallback() {
            @Override
            public void onSuccess() {
                LogUtils.e(TAG, "tim logout success.");
                PreferencesUtils.remove(ToTwooApplication.baseContext, CommonArgs.TIM_SIG);
                PreferencesUtils.remove(ToTwooApplication.baseContext, CommonArgs.TIM_SIG_TIME_MILLION);
            }

            @Override
            public void onError(int errorCode, String errorMessage) {
                LogUtils.e(TAG, "tim logout error, errorCode: " + errorCode + ", errorMessage: " + errorMessage);
            }
        });
    }

}
