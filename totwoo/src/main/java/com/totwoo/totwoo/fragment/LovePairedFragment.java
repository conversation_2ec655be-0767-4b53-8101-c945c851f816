package com.totwoo.totwoo.fragment;

import static com.totwoo.totwoo.ToTwooApplication.baseContext;
import static com.totwoo.totwoo.ToTwooApplication.owner;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.res.ResourcesCompat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.etone.framework.annotation.EventInject;
import com.etone.framework.annotation.InjectUtils;
import com.etone.framework.event.EventData;
import com.etone.framework.event.SubscriberListener;
import com.etone.framework.event.TaskType;
import com.facebook.FacebookCallback;
import com.facebook.FacebookException;
import com.facebook.share.Sharer;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.totwoo.library.bitmap.BitmapHelper;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.ConnectInfoActivity;
import com.totwoo.totwoo.activity.LoveSpacePinkActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeBaseActivity;
import com.totwoo.totwoo.bean.BQItemDataBean;
import com.totwoo.totwoo.bean.CertificationBean;
import com.totwoo.totwoo.bean.LoveTotwooInfo;
import com.totwoo.totwoo.bean.OnlineStatueEventData;
import com.totwoo.totwoo.bean.RankInfoBean;
import com.totwoo.totwoo.bean.TogetherInfo;
import com.totwoo.totwoo.bean.eventbusObject.TotwooMessage;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.BleParams;
import com.totwoo.totwoo.ble.JewInfoSingleton;
import com.totwoo.totwoo.data.CoupleLogic;
import com.totwoo.totwoo.data.TotwooLogic;
import com.totwoo.totwoo.newConrtoller.UpdatePictureController;
import com.totwoo.totwoo.tim.TimInitBusiness;
import com.totwoo.totwoo.utils.ACache;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.DateUtil;
import com.totwoo.totwoo.utils.DialogHelper;
import com.totwoo.totwoo.utils.EdgeToEdgeUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.NetUtils;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ShareUtilsSingleton;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.totwoo.totwoo.utils.ViewUtil;
import com.totwoo.totwoo.widget.AxxPagView;
import com.totwoo.totwoo.widget.CertificationLevelDialog;
import com.totwoo.totwoo.widget.CertificationShareDialog;
import com.totwoo.totwoo.widget.CommonShareType;
import com.totwoo.totwoo.widget.CustomDialog;
import com.totwoo.totwoo.widget.CustomTypefaceSpan;
import com.totwoo.totwoo.widget.SendBQDialogControllerV3;
import com.totwoo.totwoo.widget.SettingDialog;
import com.totwoo.totwoo.widget.TopLayerLayout;
import com.umeng.analytics.MobclickAgent;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import rx.Observer;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by totwoo on 2018/11/6.
 */
@SuppressLint("NonConstantResourceId")
public class LovePairedFragment extends BaseFragment implements SubscriberListener {
    public static final String IS_TOTWOO_HINT_SHOWED = "is_totwoo_hint_showed";
    public static final String IS_TOTWOO_HINT_SHOULD_SHOW = "is_totwoo_hint_should_showed";

    @BindView(R.id.love_notify_top_layout)
    TopLayerLayout mNotifyTopLayout;

    @BindView(R.id.fragment_love_bg_iv)
    ImageView imageLayer;
    @BindView(R.id.love_totwoo_tv)
    TextView totwooSendTv;
    @BindView(R.id.love_totwoo_iv)
    ImageView totwooHeartIv;
    @BindView(R.id.love_self_connect_iv)
    ImageView selfConnectIv;
    @BindView(R.id.love_other_connect_iv)
    ImageView otherConnectIv;
    @BindView(R.id.love_chat_iv)
    ImageView loveChatIv;
    @BindView(R.id.love_together_tv)
    TextView togetherTv;
    @BindView(R.id.love_twoo_tv)
    TextView twooTv;
    @BindView(R.id.love_self_iv)
    ImageView selfIv;
    @BindView(R.id.love_other_iv)
    ImageView otherIv;



    @BindView(R.id.love_message_layout_v2)
    ConstraintLayout loveMessageClV2;

    @BindView(R.id.pag_bq)
    AxxPagView pagBq;

    @BindView(R.id.love_message_head_icon_v2)
    ImageView messageHeadIconV2;
    @BindView(R.id.love_message_info_iv_v2)
    ImageView messageInfoIvV2;
    @BindView(R.id.love_message_info_tv_v2)
    TextView messageInfoTvV2;

    @BindView(R.id.love_message_date_tv)
    TextView messageInfoDataTv;

    @BindView(R.id.love_pair_content_cl)
    View love_pair_content_cl;


    private ImageView changeImageIcon;
    private ImageView helpImageIcon;
    private CustomDialog changeBgDialog;
    private CustomDialog sendTotwooDialog;
    private Context mContext;
    private TotwooLogic totwooLogic;
    private SendBQDialogControllerV3 sendBQDialogController;
    private Handler mHandler;
    private CustomTypefaceSpan tfspan;
    private Typeface typefaceGithic;
    private TogetherInfo togetherInfo;
    private ACache aCache;
    private FacebookCallback<Sharer.Result> facebookCallback;

    private SettingDialog settingDialog;
    private boolean isSettingDialogShowing = false;

    private int partner_gender;

    /**
     * 当前 Fragment 正在展示
     */
    private boolean isShowing = false;
    // v1,v2 不同版本不同功能
//    public int jewVersion;

    public static LovePairedFragment newInstance() {
        return new LovePairedFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        aCache = ACache.get(ToTwooApplication.baseContext);

        EdgeToEdgeUtils.setupBottomInsetsForLoveHomeAct(love_pair_content_cl);

        InjectUtils.injectOnlyEvent(this);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_love, container, false);
        ButterKnife.bind(this, view);


        String jewName = PreferencesUtils.getString(baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        if (!TextUtils.equals(jewName, BleParams.JEWELRY_BLE_NAME_SL) && !BleParams.isLollipopJewelry() && !BleParams.isWishJewlery()) {
            isShowing = true;
        }


        mContext = this.getContext();

        helpImageIcon = mNotifyTopLayout.getRightIcon();
        changeImageIcon = mNotifyTopLayout.getmRight2Icon();

        refreshJewState();
        refreshUI();
        File f = new File(CommonArgs.LOVE_PAIR_BACKGROUND_IMAGE);
        if (f.exists() && PermissionUtil.storagePermissionCheck(getActivity()) && f.length() > 10) {
            Glide.with(this).load(f).apply(RequestOptions.placeholderOf(R.drawable.new_heart_pair_bg)).into(imageLayer);
        }

        initTotwooHolder();

        Animation anim = AnimationUtils.loadAnimation(mContext, R.anim.totwoo_button_scale_anim);
        anim.setRepeatCount(Animation.INFINITE);
        totwooHeartIv.startAnimation(anim);

        String otherHead = PreferencesUtils.getString(baseContext, CoupleLogic.PAIRED_PERSON_HEAD_URL_TAG, "");
        BitmapHelper.setHead(getContext(), selfIv, ToTwooApplication.owner.getHeaderUrl(), owner.getGender());
        BitmapHelper.setHead(getContext(), otherIv, otherHead, partner_gender);
        return view;
    }

    private void initTotwooHolder() {

        mHandler = new Handler(Looper.getMainLooper());
        totwooLogic = new TotwooLogic(mContext);
        sendBQDialogController = new SendBQDialogControllerV3(mContext, totwooLogic);

        totwooLogic.setTotwooSendCallBack(new TotwooLogic.TotwooSendCallBack() {
            @Override
            public void onSuccess() {
            }

            @Override
            public void onFailed(String error_msg) {
                ToastUtils.showLong(mContext, error_msg);
                totwooSendTv.setText(R.string.home_totowo_holder_send_totwoo_failed);
                mHandler.postDelayed(() -> totwooSendTv.setText(R.string.home_totwoo_holder_send_totwoo_bt_text), 3000);
            }
        });
    }

    public void sendTotwoo()  {
        if (totwooLogic == null) {
            totwooLogic = new TotwooLogic(mContext);
        }


        if (sendTotwooDialog != null && sendTotwooDialog.isShowing()) return;


        if (!NetUtils.isConnected(mContext)) {
            ToastUtils.show(mContext, mContext.getString(R.string.error_net), Toast.LENGTH_SHORT);
            return;
        }

        String asString = aCache.getAsString(CommonArgs.EMOJI_LIST);
        if (!TextUtils.isEmpty(asString)) {
            ArrayList<BQItemDataBean> emolist = new Gson().fromJson(asString, new TypeToken<ArrayList<BQItemDataBean>>() {
            }.getType());
            if (emolist.size() == 5 ) {
                //  ToTwooApplication.cacheData.setEmojiList(emolist);
                sendBQDialogController.setData(emolist);
                sendTotwooDialog = sendBQDialogController.showBQDialog(true);
            } else {
                aCache.remove(CommonArgs.EMOJI_LIST);
                getEmojList();
            }
        } else {
            getEmojList();
        }
    }

    private void getEmojList() {
        launchRequest(
                HttpHelper.commonService.getEmojiListNew(ToTwooApplication.otherPhone),
                emoList -> {
                    if (emoList == null || emoList.isEmpty()) {
                        emoList = CommonUtils.getDefaultBQ();
                    }
                    // 使用一个 Map 来优化查找和更新效率
                    for (BQItemDataBean dataBean : emoList) {
                        String notifyColor = dataBean.getNotify_color();
                        dataBean.setNotify_color(NotifyUtil.getJewColorName(notifyColor));//色值 转为名字
                    }

                    sendBQDialogController.setData(emoList);
                    sendTotwooDialog = sendBQDialogController.showBQDialog(true);

//                    ToTwooApplication.cacheData.setEmojiList(emoList);
                    aCache.put(CommonArgs.EMOJI_LIST, new Gson().toJson(emoList));
                }
        );
    }

    private void animAndGoneMessage() {
        View[] views = new View[1];
        if (loveMessageClV2.getVisibility() == View.VISIBLE) {
            views[0] = loveMessageClV2;
        }

        // 执行动画
        ViewUtil.showParallaxAnim(mContext, views, 100, R.anim.home_message_out, new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                refreshMessageLayout(false);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
    }


    @EventInject(eventType = S.E.E_UPDATE_JEWERLY_READ_VERSION, runThread = TaskType.UI)
    public void jewrlyReadVersion(EventData data) {
        initTotwooHolder();
    }

    @EventInject(eventType = S.E.E_HOMEACTIVITY_ONSHOW, runThread = TaskType.UI)
    public void onEventShow(EventData data) {
        refreshJewState();
    }

    @Override
    public void onShow() {
        super.onShow();
        isShowing = true;
        MobclickAgent.onPageStart(TrackEvent.LOVE_PAGE);
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.HOMEPAGE_BOTTOM_LOVE);

        if (getActivity() == null) {
            return;
        }

        CommonUtils.setStateBar(getActivity(), true);

        if (loveChatIv != null) {
            if (!HomeBaseActivity.hasUnread) {
                loveChatIv.setImageResource(R.drawable.love_fragment_talk_icon);
            } else {
                loveChatIv.setImageResource(R.drawable.love_fragment_talk_unread_icon);
            }
        }
    }

    @Override
    public void onHide() {
        super.onHide();
        isShowing = false;
        if (getActivity() == null) {
            return;
        }
//        if (loveMessageCl != null && loveMessageCl.getVisibility() == View.VISIBLE) {
//            refreshMessageLayout(false);
//        }
    }


    /**
     * 收到 IM 信息
     */
    public void receiveImMessage() {
        loveChatIv.setImageResource(R.drawable.love_fragment_talk_unread_icon);
    }



    /**
     * 收到 Totwoo 信息
     *
     * @param message
     */
    public void receiveTotwooMessageV2(TotwooMessage message) {
        if (message.getMessageBean() == null) {
            return;
        }

        String contentCode = message.getMessageBean().getContent();
        if (TextUtils.isEmpty(contentCode)) {
            message.getMessageBean().setContent(CommonUtils.CONTENT_MISS);
        }
        loveMessageClV2.setVisibility(View.VISIBLE);
        loveMessageClV2.setAlpha(1);

        pagBq.setUpFile(CommonUtils.getPagPath(contentCode));
        pagBq.playAnimation();

        aCache.remove(CommonArgs.NOTIFICATION_MESSAGE);
        refreshMessageLayout(true);
        BitmapHelper.setHead(ToTwooApplication.baseContext, messageHeadIconV2, message.getMessageBean().getPicUrl(), partner_gender);
        messageInfoIvV2.setImageResource(switchBQByContentV2(contentCode));
        messageInfoTvV2.setText(message.getMessageBean().getNotify_content());
        messageInfoDataTv.setText(DateUtil.getTimeString(getContext(), message.getMessageBean().getSendTime()));
        // 刷新次数
        refreshTotwooCount();
        View[] viewsMessage = new View[]{loveMessageClV2};
        // 执行动画
        ViewUtil.showParallaxAnim(mContext, viewsMessage, 100, R.anim.home_message_in, null);

        //隐藏dialog
        if (sendTotwooDialog != null && sendTotwooDialog.isShowing()) {
            sendTotwooDialog.dismiss();
        }
    }


    /**
     * totwoo 发送成功
     *
     * @param message
     */
    public void totwooSendSuccess(TotwooMessage message) {
        // 刷新次数
        refreshTotwooCount();
        // 回复 Totwoo 成功
        if ( loveMessageClV2.getVisibility() == View.VISIBLE) {
            animAndGoneMessage();
        } else {
            totwooSendTv.setText(R.string.send_success);
            mHandler.postDelayed(() -> totwooSendTv.setText(R.string.home_totwoo_holder_send_totwoo_bt_text), 3000);
        }
    }

    /**
     * 背景切换
     *
     * @param imageUri
     */
    public void changeBackgroundImage(Uri imageUri) {
        if (changeBgDialog != null && changeBgDialog.isShowing()) {
            changeBgDialog.dismiss();
        }

        if (imageUri == null) {
            imageLayer.setBackgroundResource(R.drawable.new_heart_pair_bg);
        } else {
            RequestOptions requestOptions = RequestOptions.errorOf(R.drawable.new_heart_pair_bg);

            if ("file".equalsIgnoreCase(imageUri.getScheme())) {
                UpdatePictureController.getInstance().uploadPairBackgroundPictures(ToTwooApplication.baseContext, imageUri.getPath());
                requestOptions = requestOptions.skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE);
            }

            Glide.with(this).setDefaultRequestOptions(requestOptions).load(imageUri).into(imageLayer);
        }
    }

    private int switchBQByContent(String content) {
        if (content == null || content.length() == 0) return R.drawable.bq_totwoo_big;
        else if (content.equals("1001")) //想你
            return R.drawable.bq_miss_big;
        else if (content.equals("1002"))    //亲亲
            return R.drawable.bq_kiss_big;
        else if (content.equals("1003")) return R.drawable.bq_pain_big;  //打你
        else if (content.equals("1004")) return R.drawable.bq_sorry_big; //对不起
        else if (content.equals("1006")) return R.drawable.bq_hurt_big;
        else if (content.equals("1009")) return R.drawable.bq_love_big;
        else return R.drawable.bq_totwoo_big;    //默认没有符合4个图标都，全部都是Totwoo
    }

    private int switchBQByContentV2(String content) {
        return CommonUtils.getImg(content);
    }

    @OnClick({R.id.love_home_iv, R.id.love_chat_iv, R.id.love_totwoo_rl, R.id.love_fragment_info_iv, R.id.love_self_iv, R.id.love_other_iv, R.id.fragment_love_bg_iv})

    //     case R.id.love_other_iv:
    //            case R.id.love_self_iv:
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.love_home_iv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_PAIR_COZONE);
                startActivity(new Intent(getActivity(), LoveSpacePinkActivity.class));
                break;
            case R.id.love_chat_iv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_PAIR_IM);
                TimInitBusiness.navToChat();
                HomeBaseActivity.hasUnread = false;
                loveChatIv.setImageResource(R.drawable.love_fragment_talk_icon);
                break;
            case R.id.love_totwoo_rl:
                if (loveMessageClV2.getVisibility() == View.VISIBLE) {
                    animAndGoneMessage();
                }
                sendTotwoo();
                break;
            case R.id.love_unpaired_content_rl:
                break;
            case R.id.love_other_iv:
            case R.id.love_self_iv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_CLICK_CONPLEHEADERIMAGE);
                startActivity(new Intent(getActivity(), LoveSpacePinkActivity.class));
                break;
            case R.id.love_fragment_info_iv:
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_CLICK_CONNUM);
                startActivity(new Intent(getActivity(), ConnectInfoActivity.class));
                break;
            case R.id.fragment_love_bg_iv:
                if ( loveMessageClV2.getVisibility() == View.VISIBLE) {
                    animAndGoneMessage();
                }
                break;
        }
    }

    public void refreshJewState() {
        if (getActivity() == null) {
            return;
        }
        if (mNotifyTopLayout != null) {
            mNotifyTopLayout.setJewState();
        }

        if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_CONNECTED) {
            selfConnectIv.setImageResource(R.drawable.love_connect_icon);
        } else {
            initTotwooHolder();
            selfConnectIv.setImageResource(R.drawable.love_un_connect_icon);
        }
    }


    /**
     * 顶部标签页点击闪光
     * CustomAngelPullHolder
     */
    @EventInject(eventType = S.E.E_ONLINE_STATUE_CHANGE, runThread = TaskType.UI)
    public void onReceiveOnlineStatue(EventData data) {
        if (TextUtils.isEmpty(((OnlineStatueEventData) data).getTarget_firmware_type())) {
            otherConnectIv.setImageResource(R.drawable.love_un_connect_icon);
        } else {
            otherConnectIv.setImageResource(R.drawable.love_connect_icon);
        }
    }


    //更新对方状态
    protected void initLoveTotwooInfo(LoveTotwooInfo loveTotwooInfo) {
        if (TextUtils.isEmpty(loveTotwooInfo.getTarget_connect())) {
            otherConnectIv.setImageResource(R.drawable.love_un_connect_icon);
        } else {
            otherConnectIv.setImageResource(R.drawable.love_connect_icon);
        }

        partner_gender = TextUtils.isEmpty(aCache.getAsString(CommonArgs.PARTNER_GENDER)) ? (1 - owner.getGender()) : Integer.parseInt(aCache.getAsString(CommonArgs.PARTNER_GENDER));

        BitmapHelper.setHead(getContext(), selfIv, ToTwooApplication.owner.getHeaderUrl(), owner.getGender());
        BitmapHelper.setHead(getContext(), otherIv, loveTotwooInfo.getHead(), partner_gender);

        // 配对状态差异
        refreshTotwooCount();
    }

    private void refreshMessageLayout(boolean isShow) {
        if (isShow) {
//            if (jewVersion == 1) {
//                loveMessageCl.setVisibility(View.VISIBLE);
//            } else {
            loveMessageClV2.setVisibility(View.VISIBLE);
//            }
        } else {

            if (loveMessageClV2.getVisibility() == View.VISIBLE) {
                loveMessageClV2.setVisibility(View.GONE);
            }
        }
    }


    private void refreshUI() {
//        if (Apputils.systemLanguageIsChinese(ToTwooApplication.baseContext)) {
//            helpImageIcon.setVisibility(View.VISIBLE);
//            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(CommonUtils.dip2px(ToTwooApplication.baseContext, 17), CommonUtils.dip2px(ToTwooApplication.baseContext, 17));
//            layoutParams.gravity = Gravity.END | Gravity.TOP;
//            layoutParams.setMargins(0, CommonUtils.dip2px(ToTwooApplication.baseContext, 48), CommonUtils.dip2px(ToTwooApplication.baseContext, 18), 0);
//            helpImageIcon.setLayoutParams(layoutParams);
//            helpImageIcon.setImageResource(R.drawable.icon_help_white);
//            helpImageIcon.setOnClickListener(v -> DialogHelper.getInstance().getTotwooHelpDialog(getContext()).show());
//            changeImageIcon.setVisibility(View.VISIBLE);
//            changeImageIcon.setImageResource(R.drawable.love_fragment_bg_icon);
//            changeImageIcon.setOnClickListener(v -> changeMainBg());
//        } else {
        helpImageIcon.setVisibility(View.VISIBLE);
        helpImageIcon.setImageResource(R.drawable.love_fragment_bg_icon);
        helpImageIcon.setOnClickListener(v -> changeMainBg());
//        }

        if (isShowing) {
            CommonUtils.setStateBar(getActivity(), true);
        }
    }

    /**
     * 弹出选择窗,  切换背景
     */
    private void changeMainBg() {
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.LOVE_PAIR_TOP_CHANGE_IMAGE);
        changeBgDialog = DialogHelper.getInstance().getLoveChangeBbDialog(getContext(), v -> {

            launchRequest(HttpHelper.commonService.updatePairBg(""), data -> {
                imageLayer.setImageResource(R.drawable.new_heart_pair_bg);
                File f = new File(CommonArgs.LOVE_PAIR_BACKGROUND_IMAGE);
                if (f.exists()) {
                    f.delete();
                }
                changeBgDialog.dismiss();
            });
        });
        changeBgDialog.show();
    }

    private CharSequence setTextSpan(int resourceId, int number) {
        if (typefaceGithic == null) {
            typefaceGithic = ResourcesCompat.getFont(mContext, R.font.gothicb);
        }

        if (tfspan == null) {
            tfspan = new CustomTypefaceSpan(typefaceGithic);
        }

        SpannableString spannableString = new SpannableString(mContext.getString(resourceId, number + ""));
        int index = spannableString.toString().indexOf(number + "");
        spannableString.setSpan(new AbsoluteSizeSpan(14, true), index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#000000")), index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(tfspan, index, index + (number + "").length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        return spannableString;
    }

    /**
     * 收到 totwoo, 发送 totwoo 成功, 获取到当前为配对状态是, 均会调用此方法, 刷新 totwoo 数
     */
    private void refreshTotwooCount() {
        HttpHelper.commonService.getTogetherInfo(ToTwooApplication.owner.getPairedId()).compose(HttpHelper.<HttpBaseBean<TogetherInfo>>rxSchedulerHelper()).subscribe(new Subscriber<HttpBaseBean<TogetherInfo>>() {
            @Override
            public void onCompleted() {
                // 通知 totwoo 数量更新; 状态更新.
                //重复调用
//                        com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_PAIRED_STATE, null);
            }

            @Override
            public void onError(Throwable e) {
                LogUtils.e("refresh error.", e);
            }

            @Override
            public void onNext(HttpBaseBean<TogetherInfo> togetherInfoHttpBaseBean) {
                if (togetherInfoHttpBaseBean.getErrorCode() == 0) {
                    if (ToTwooApplication.cacheData != null) {
                        ToTwooApplication.cacheData.setTogetherDay(togetherInfoHttpBaseBean.getData().getTogether_data() + "");
                    }
//                    aCache.put(HistoryTwooActivity.HISTORY_TOGETHER_DAY, togetherInfoHttpBaseBean.getData().getTogether_data() + "");
//                    togetherTv.setText(setTextSpan(R.string.love_together, togetherInfoHttpBaseBean.getData().getTogether_data()));
                    int consonance_count = togetherInfoHttpBaseBean.getData().getConsonance_count();
                    twooTv.setText(setTextSpan(R.string.love_collect, consonance_count));
                    PreferencesUtils.put(getContext(), CoupleLogic.PAIRED_TOTWOO_COUNT, consonance_count);
                    //重复调用
//                            com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_PAIRED_STATE, null);
                    togetherInfo = togetherInfoHttpBaseBean.getData();
                    int level = togetherInfo.getGet_sign();
                    if (level > 0) {
                        showLevelDialog(CommonArgs.getCerList().get(level - 1));
                    }
                } else {
//                            aCache.remove(HistoryTwooActivity.HISTORY_TOGETHER_DAY);
//                            togetherTv.setText(setTextSpan(R.string.love_together, 1));
                    twooTv.setText(setTextSpan(R.string.love_collect, 0));
                }
            }
        });
    }

    private CertificationLevelDialog certificationLevelDialog;

    private void showLevelDialog(CertificationBean bean) {
        if (getContext() == null) {
            return;
        }

        if (certificationLevelDialog == null) {
            certificationLevelDialog = new CertificationLevelDialog(getContext());
        }

        certificationLevelDialog.setKeyFrom("home");
        certificationLevelDialog.setCount(bean.getLevelCount(), bean.getResImg());
        certificationLevelDialog.setInfo(getResources().getString(bean.getResName()));
//        certificationLevelDialog.setCheckTv(v -> {
//            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.CERTIFICATION_DIALOG);
//            startActivity(new Intent(getContext(), LoveCertificationActivity.class));
//            certificationLevelDialog.dismiss();
//        });
//        certificationLevelDialog.setShare(v -> {
//            certificationLevelDialog.dismiss();
//            HttpHelper.commonService.getRankInfo(ToTwooApplication.owner.getPairedId(), 100)
//                    .compose(HttpHelper.rxSchedulerHelper())
//                    .subscribe(new Observer<HttpBaseBean<RankInfoBean>>() {
//                        @Override
//                        public void onCompleted() {
//
//                        }
//
//                        @Override
//                        public void onError(Throwable e) {
//                            ToastUtils.showShort(mContext, R.string.error_net);
//                        }
//
//                        @Override
//                        public void onNext(HttpBaseBean<RankInfoBean> rankInfoBeanHttpBaseBean) {
//                            if (rankInfoBeanHttpBaseBean.getErrorCode() == 0) {
//                                showShareDialog(rankInfoBeanHttpBaseBean.getData());
//                            }
//                        }
//                    });
//        });
        certificationLevelDialog.show();
    }

    private CertificationShareDialog certificationShareDialog;

    private void showShareDialog(RankInfoBean rankInfoBean) {
        if (certificationShareDialog == null) {
            if (Apputils.systemLanguageIsChinese(mContext)) {
                List<CommonShareType> types = new ArrayList<>();
                types.add(CommonShareType.FRIENDS);
                types.add(CommonShareType.WECHAT);
                types.add(CommonShareType.WEIBO);
                types.add(CommonShareType.QZONE);
                types.add(CommonShareType.QQ);
                certificationShareDialog = new CertificationShareDialog(mContext, types, v -> {
                    switch ((CommonShareType) v.getTag()) {
                        case FRIENDS:
                            ShareUtilsSingleton.getInstance().shareImageToWechatMoment(getPath(certificationShareDialog.getShareView()));
                            certificationShareDialog.dismiss();
                            break;
                        case WECHAT:
                            ShareUtilsSingleton.getInstance().shareImageToWechat(getPath(certificationShareDialog.getShareView()));
                            certificationShareDialog.dismiss();
                            break;
                        case WEIBO:
                            ShareUtilsSingleton.getInstance().shareImageToWeibo(getActivity(), getPath(certificationShareDialog.getShareView()), "");
                            certificationShareDialog.dismiss();
                            break;
                        case QZONE:
                            ShareUtilsSingleton.getInstance().shareImageToQzone(getPath(certificationShareDialog.getShareView()), "");
                            certificationShareDialog.dismiss();
                            break;
                        case QQ:
                            ShareUtilsSingleton.getInstance().shareImageToQQ(getPath(certificationShareDialog.getShareView()));
                            certificationShareDialog.dismiss();
                            break;
                    }

                });
                certificationShareDialog.setCanceledOnTouchOutside(false);
//                certificationShareDialog.setCustomTitle(CommonUtils.setNumberOrangeSpan(getResources().getString(R.string.share_text_head_info), 88, 17));
            } else {
                facebookCallback = new FacebookCallback<Sharer.Result>() {
                    @Override
                    public void onSuccess(Sharer.Result result) {
                        ToastUtils.showShort(mContext, getResources().getString(R.string.share_complete));
                    }

                    @Override
                    public void onCancel() {
                        ToastUtils.showShort(mContext, getResources().getString(R.string.share_cancel));
                    }

                    @Override
                    public void onError(FacebookException error) {
                        ToastUtils.showShort(mContext, getResources().getString(R.string.share_error));
                    }
                };
                List<CommonShareType> types = new ArrayList<>();
                types.add(CommonShareType.FACEBOOK);
                types.add(CommonShareType.TWITTER);
                types.add(CommonShareType.FRIENDS);
                types.add(CommonShareType.WECHAT);
                certificationShareDialog = new CertificationShareDialog(mContext, types, v -> {
                    switch ((CommonShareType) v.getTag()) {
                        case FRIENDS:
                            ShareUtilsSingleton.getInstance().shareImageToWechatMoment(getPath(certificationShareDialog.getShareView()));
                            certificationShareDialog.dismiss();
                            break;
                        case WECHAT:
                            ShareUtilsSingleton.getInstance().shareImageToWechat(getPath(certificationShareDialog.getShareView()));
                            certificationShareDialog.dismiss();
                            break;
                        case FACEBOOK:
                            ShareUtilsSingleton.getInstance().shareImageToFacebook(getPath(certificationShareDialog.getShareView()), getActivity(), facebookCallback);
                            certificationShareDialog.dismiss();
                            break;
                        case TWITTER:
                            ShareUtilsSingleton.getInstance().shareImageToTwitter(getPath(certificationShareDialog.getShareView()), "");
                            certificationShareDialog.dismiss();
                            break;
                    }

                });
                certificationShareDialog.setCanceledOnTouchOutside(false);
//                certificationShareDialog.setCustomTitle(getResources().getString(R.string.share_text_head_info));
            }
            int level = rankInfoBean.getGet_sign() - 1 > 0 ? rankInfoBean.getGet_sign() - 1 : 0;
            certificationShareDialog.setBackground(CommonArgs.mainImages[level]);
            certificationShareDialog.setContent(CommonArgs.contentImages[level]);
            certificationShareDialog.setInfo(getString(CommonArgs.CERTIFICATION_LEVEL_INFO_IDS[level]));
            certificationShareDialog.setCount(CommonArgs.CERTIFICATION_LEVEL_COUNT[level]);
            certificationShareDialog.setUserInfo(rankInfoBean.getUserinfo());
        }
        certificationShareDialog.show();
    }

    private String getPath(View view) {
        Bitmap snapShareBitmap = ShareUtilsSingleton.getBitmapByView(view);
        return FileUtils.saveBitmapFromSDCard(snapShareBitmap, "totwoo_cache_img_" + System.currentTimeMillis());
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        InjectUtils.injectUnregisterListenerAll(this);
    }

    @Override
    public void onEventException(String eventType, EventData data, Throwable e) {

    }
}
