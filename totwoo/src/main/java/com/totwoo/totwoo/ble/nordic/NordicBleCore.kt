package com.totwoo.totwoo.ble.nordic

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCharacteristic
import android.content.Context
import android.os.Build
import android.text.TextUtils
import com.tencent.mars.xlog.Log
import com.totwoo.library.util.LogUtils
import com.totwoo.totwoo.bean.JewSettings
import com.totwoo.totwoo.ble.AppPackege
import com.totwoo.totwoo.ble.BleParams
import com.totwoo.totwoo.ble.BleUtils
import com.totwoo.totwoo.ble.JewInfoSingleton
import com.totwoo.totwoo.utils.PreferencesUtils
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import no.nordicsemi.android.ble.BleManager
import no.nordicsemi.android.ble.DisconnectRequest
import no.nordicsemi.android.ble.data.Data
import no.nordicsemi.android.support.v18.scanner.ScanRecord
import java.util.TimeZone
import java.util.UUID

/**
 * Nordic BLE核心管理类 - 替代BluetoothWrapper
 *
 * 功能：
 * 1. 连接管理：连接、断开、重连、状态管理
 * 2. 协议通信：发送指令、接收数据、队列管理
 * 3. 数据解析：电量、充电状态、固件信息、设备信息
 * 4. 事件监听：触摸、点击、固件上报等
 * 5. OTA支持：OTA模式切换、固件升级
 */
class NordicBleCore(private val appContext: Context) : BleManager(appContext) {

    companion object {
        private const val TAG = "NordicBleCore"
    }

    // 特征值引用 - 与原有保持一致
    private var batteryLevelChar: BluetoothGattCharacteristic? = null
    private var writeChar: BluetoothGattCharacteristic? = null
    private var notifyChar: BluetoothGattCharacteristic? = null
    private var firmwareRevisionChar: BluetoothGattCharacteristic? = null
    private var chargeChar: BluetoothGattCharacteristic? = null
    private var controlChar: BluetoothGattCharacteristic? = null
    private var controlNotifyChar: BluetoothGattCharacteristic? = null
    private var modelNoChar: BluetoothGattCharacteristic? = null
    private var hardwareChar: BluetoothGattCharacteristic? = null
    private var psnChar: BluetoothGattCharacteristic? = null

    // 回调接口 - 与原有BluetoothWrapper.BluetoothStatusListener完全兼容
    interface StatusListener {
        fun onConnected()
        fun onDisconnected()
        fun onBatteryChanged(level: Int)
        fun onWriteSuccessed()
        fun onOTAWriteSuccessed()
        fun onJewClicked()
        fun onJewTripleClicked()
        fun onJewTouched()
        fun onFirstConnect()
        fun onImeiGetSuccessed()
        fun onScanAddressOverTime()
        fun onNeedNotificationSed()
        fun onNeedNotificationStep()
    }

    private var statusListener: StatusListener? = null

    // 皓月配置数据流 - 直接暴露业务数据，带重放缓存
    private val _jewSettingsFlow = MutableSharedFlow<com.totwoo.totwoo.bean.JewSettings>(
        replay = 1, // 缓存最新的1个值
        extraBufferCapacity = 0
    )
    val jewSettingsFlow: SharedFlow<com.totwoo.totwoo.bean.JewSettings> =
        _jewSettingsFlow.asSharedFlow()

    fun setStatusListener(listener: StatusListener?) {
        this.statusListener = listener
    }

    override fun getGattCallback(): BleManagerGattCallback {
        return TotwooGattCallback()
    }

    init {
        // 使用Nordic BLE标准的连接状态监听 - 简化版
        connectionObserver = object : no.nordicsemi.android.ble.observer.ConnectionObserver {
            override fun onDeviceConnected(device: BluetoothDevice) {
                JewInfoSingleton.getInstance().connectState = JewInfoSingleton.STATE_CONNECTED
                statusListener?.onConnected()
            }

            override fun onDeviceDisconnected(
                device: BluetoothDevice, reason: Int
            ) {
                JewInfoSingleton.getInstance().connectState = JewInfoSingleton.STATE_DISCONNECTED
                statusListener?.onDisconnected()
            }

            override fun onDeviceConnecting(device: BluetoothDevice) {
                JewInfoSingleton.getInstance().connectState = JewInfoSingleton.STATE_RECONNECTING
            }

            override fun onDeviceDisconnecting(device: BluetoothDevice) {
                // 断开中状态，无需特殊处理
                LogUtils.d(TAG, "onDeviceDisconnecting")
            }

            override fun onDeviceFailedToConnect(
                device: BluetoothDevice, reason: Int
            ) {
                JewInfoSingleton.getInstance().connectState = JewInfoSingleton.STATE_DISCONNECTED
                statusListener?.onDisconnected()
            }

            override fun onDeviceReady(device: BluetoothDevice) {
                // 设备就绪，无需特殊处理
            }
        }
    }

    /**
     * Nordic BLE GATT回调实现
     */
    private inner class TotwooGattCallback : BleManagerGattCallback() {

        override fun isRequiredServiceSupported(gatt: BluetoothGatt): Boolean {
            LogUtils.d(TAG, "检查服务支持")

            // 获取所有特征值 - 复用原有UUID定义
            val services = gatt.services
            for (service in services) {
                for (characteristic in service.characteristics) {
                    when (BleUtils.getSimpleUUID(characteristic.uuid)) {
                        BleParams.UUID_CHAR_BATTERY_LEVEL -> batteryLevelChar = characteristic
                        BleParams.UUID_CHAR_WRITE -> writeChar = characteristic
                        BleParams.UUID_CHAR_NOTIFY -> notifyChar = characteristic
                        BleParams.UUID_CHAR_FIRMWARE_REVISION -> firmwareRevisionChar =
                            characteristic

                        BleParams.UUID_CHARGE_STATE_CHAR -> chargeChar = characteristic
                        BleParams.UUID_CHAR_CONTROL -> controlChar = characteristic
                        BleParams.UUID_CHAR_CONTROL_NOTIFY -> controlNotifyChar = characteristic
                        BleParams.UUID_CHAR_MODEL_NUMBER -> modelNoChar = characteristic
                        BleParams.UUID_CHAR_HARDWARE_REVISION -> hardwareChar = characteristic
                        BleParams.UUID_CHAR_PSN -> psnChar = characteristic
                    }
                }
            }

            // 检查必需的特征值是否存在
            val required = writeChar != null && notifyChar != null
            LogUtils.d(TAG, "服务支持检查结果: $required")
            return required
        }

        override fun initialize() {
            LogUtils.d(TAG, "Nordic BLE: 初始化设备特征值")
            enableAllNotifications()
            readDeviceInfo()
            initializeDevice()
        }

        override fun onServicesInvalidated() {
            LogUtils.d(TAG, "Nordic BLE: 服务失效，清理特征值引用")
            clearCharacteristics()
        }
    }

    // ========== 连接管理 ==========

    /**
     * 连接设备 - 优先使用系统自动重连
     */
    fun connectDevice(device: BluetoothDevice) {
        // 检查当前连接状态，避免重复连接
        if (isConnected) {
            LogUtils.d(TAG, "设备已连接，无需重连")
            return
        }

        // 检查是否正在连接中
        if (connectionState == android.bluetooth.BluetoothProfile.STATE_CONNECTING) {
            LogUtils.d(TAG, "设备正在连接中，跳过重连")
            return
        }
        connect(device).useAutoConnect(true) // 优先使用自动重连，让系统管理
            .enqueue()
    }

    /**
     * 重连设备 - 智能重连逻辑
     */
    @SuppressLint("MissingPermission")
    fun reconnectDevice() {
        // 检查当前连接状态，避免重复连接
        if (isConnected) {
            LogUtils.d(TAG, "设备已连接，无需重连")
            return
        }

        // 检查是否正在连接中
        if (connectionState == android.bluetooth.BluetoothProfile.STATE_CONNECTING) {
            LogUtils.d(TAG, "设备正在连接中，跳过重连")
            return
        }

        val address = PreferencesUtils.getString(context, BleParams.PAIRED_BLE_ADRESS_TAG, "")
        if (TextUtils.isEmpty(address) || !BluetoothAdapter.checkBluetoothAddress(address)) {
            LogUtils.w(TAG, "地址为空或无效: $address")
            //清除数据
            return
        }

        try {
            // 获取设备对象 - getRemoteDevice()不会返回null
            val device = getBluetoothDeviceSafely(BluetoothAdapter.getDefaultAdapter(),address)
            if (device != null && !TextUtils.isEmpty(device.name)) {
                connectDevice(device) // 统一使用自动重连
            } else {
                startAddressScanForReconnect(address)
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "获取设备对象失败: ${e.message}")
            // 如果获取设备对象失败，尝试扫描方式重连
            startAddressScanForReconnect(address)
        }
    }


    private fun getBluetoothDeviceSafely(adapter: BluetoothAdapter, address: String): BluetoothDevice? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                adapter.getRemoteLeDevice(address, BluetoothDevice.ADDRESS_TYPE_PUBLIC)
            } else {
                adapter.getRemoteDevice(address)
            }
        } catch (e: IllegalArgumentException) {
            // 处理无效地址，例如返回 null 或日志
            null
        }
    }

    /**
     * 使用地址扫描进行重连
     */
    fun startAddressScanForReconnect(address: String) {
        LogUtils.d(TAG, "开始地址扫描重连: $address")
        ScanManager.startAddressScan(false,
            address, object : ScanManager.ScanCallback {
                override fun onAddressDeviceFound(device: BluetoothDevice) {
                    LogUtils.d(TAG, "扫描找到设备，开始自动连接: ${device.address}")
                    connectDevice(device) // 统一使用自动重连
                }

                override fun onDeviceFound(
                    device: BluetoothDevice, rssi: Int, scanRecord: ScanRecord?
                ) {
                }

                override fun onScanTimeout() {
                    LogUtils.w(TAG, "地址扫描超时")
                    statusListener?.onScanAddressOverTime()
                }

                override fun onScanFailed(errorCode: Int) {
                    LogUtils.e(TAG, "地址扫描失败: $errorCode")
                    statusListener?.onScanAddressOverTime()
                }

                override fun onOTADeviceFound(device: BluetoothDevice) {}
            })
    }


    /**
     * 断开连接 - 使用Nordic BLE标准方法
     */
    fun disconnectDevice(): DisconnectRequest {
        return disconnect().timeout(3000)
    }

    // ========== 协议通信 ==========

    /**
     * 写入应用数据包 - 替代原有的wirteMessage()
     * Nordic BLE库自动处理分包，无需手动分包
     */
    fun writeAppPackage(appPackage: AppPackege) {
        val data = BleUtils.packetTranData(appPackage)
        Log.d(TAG, "写入应用数据包: $appPackage")
        LogUtils.d(TAG, "数据: ${BleUtils.bytesToHexString(data)}")

        writeChar?.let { char ->
            writeCharacteristic(char, data).split() // Nordic BLE自动分包
                .with { _, _ ->
                    LogUtils.d(TAG, "写入成功")
                    statusListener?.onWriteSuccessed()
                }.fail { _, status ->
                    LogUtils.e(TAG, "写入失败: $status")
                }.enqueue()
        }
    }

    /**
     * 同步UTC时间 - 复用原有逻辑
     */
    private fun syncUtcTime() {
        val packege = AppPackege().apply {
            commandId = 0x01
            val keyValue = BleUtils.getBytes(
                (System.currentTimeMillis() / 1000 + TimeZone.getDefault().rawOffset / 1000).toInt()
            )
            data = AppPackege.DataCell(0x03, keyValue)
        }
        writeAppPackage(packege)
    }

    /**
     * 切换数据模式 - 复用原有逻辑
     */
    fun changeDataMode(mode: Int) {
        val packege = AppPackege().apply {
            commandId = 0x01
            data = AppPackege.DataCell(0x04, byteArrayOf(mode.toByte()))
        }
        writeAppPackage(packege)
    }

    /**
     * 读取历史数据 - 复用原有逻辑
     */
    fun readHistoryData() {
        val packege = AppPackege().apply {
            commandId = 0x03
            data = AppPackege.DataCell(0x01, null)
        }
        writeAppPackage(packege)
    }

    // ========== 设备信息读取 ==========

    /**
     * 读取电池电量 - 使用Nordic BLE标准方法
     */
    fun readBattery() {
        batteryLevelChar?.let { char ->
            readCharacteristic(char).with { _, data ->
                val battery = data.getIntValue(Data.FORMAT_UINT8, 0) ?: 0
                JewInfoSingleton.getInstance().battery = battery
                statusListener?.onBatteryChanged(battery)
            }.enqueue()
        }
    }


    /**
     * 读取充状态
     */
    private fun readChargeState() {
        chargeChar?.let { char ->
            readCharacteristic(char).with { _, data ->
                val charging = (data.value?.get(0) ?: 0) == 1.toByte()
                JewInfoSingleton.getInstance().isCharge = charging
            }.enqueue()
        }
    }

    /**
     * 读取固件版本 - 使用Nordic BLE标准方法
     */
    fun readFirmwareVersion() {
        firmwareRevisionChar?.let { char ->
            readCharacteristic(char).with { _, data ->
                val version = data.getStringValue(0) ?: ""
                PreferencesUtils.put(
                    context, BleParams.EXTRA_BLE_DATA_TAG_FIRMWARE_REVISION, version
                )
                com.etone.framework.event.EventBus.onPostReceived(
                    com.totwoo.totwoo.S.E.E_UPDATE_JEWERLY_READ_VERSION, null
                )
            }.enqueue()
        }
    }

    // ========== OTA相关 ==========

    /**
     * 进入OTA模式 - 使用Nordic BLE标准方法
     */
    fun enterOTAMode() {
        controlChar?.let { char ->
            val data = BleUtils.packgeControlData(0x01)
            writeCharacteristic(char, data).with { _, _ ->
                statusListener?.onOTAWriteSuccessed()
            }.enqueue()
        }
    }

    /**
     * 发送IMEI请求 - 使用Nordic BLE标准方法
     */
    fun sendIMEIRequest() {
        controlChar?.let { char ->
            val data = BleUtils.packgeControlData(0xB0)
            writeCharacteristic(char, data).with { _, _ ->
                // IMEI响应在控制通知中处理
            }.enqueue()
        }
    }

    // ========== 状态查询方法 ==========

    // 使用Nordic BLE库的isConnected()方法，无需重复实现
    // override fun isConnected(): Boolean 已由父类BleManager提供

    // ========== 私有方法 ==========

    /**
     * 启用所有通知 - 使用Nordic BLE标准方法
     */
    private fun enableAllNotifications() {
        // 启用数据通知
        notifyChar?.let { char ->
            setNotificationCallback(char).with { _, data ->
                handleNotifyData(
                    data.value ?: byteArrayOf()
                )
            }
            enableNotifications(char).enqueue()
        }

        // 启用控制通知
        controlNotifyChar?.let { char ->
            setNotificationCallback(char).with { _, data ->
                // 复用原有的控制数据解析逻辑
                parseControlNotifyData(char)
            }
            enableNotifications(char).enqueue()
        }

        // 启用电池通知
        batteryLevelChar?.let { char ->
            setNotificationCallback(char).with { _, data ->
                val battery = data.getIntValue(Data.FORMAT_UINT8, 0) ?: 0
                JewInfoSingleton.getInstance().battery = battery
                statusListener?.onBatteryChanged(battery)
            }
            enableNotifications(char).enqueue()
        }

        // 启用充电状态通知
        chargeChar?.let { char ->
            setNotificationCallback(char).with { _, data ->
                val charging = (data.value?.get(0) ?: 0) == 1.toByte()
                JewInfoSingleton.getInstance().isCharge = charging
//                statusListener?.onBatteryChanged(JewInfoSingleton.getInstance().battery)
            }
            enableNotifications(char).enqueue()
        }
    }

    /**
     * 读取设备信息 - 使用Nordic BLE标准方法
     */
    private fun readDeviceInfo() {
        // 读取电池电量
        readBattery()
        //读取充电状态
        readChargeState()

        // 读取固件版本
        readFirmwareVersion()

        // 读取其他设备信息并缓存到ToTwooApplication.cacheData
        modelNoChar?.let { char ->
            readCharacteristic(char).with { _, data ->
                com.totwoo.totwoo.ToTwooApplication.cacheData?.model_number =
                    data.getStringValue(0)
            }.enqueue()
        }

        hardwareChar?.let { char ->
            readCharacteristic(char).with { _, data ->
                com.totwoo.totwoo.ToTwooApplication.cacheData?.hardware_revision =
                    data.getStringValue(0)
            }.enqueue()
        }

        psnChar?.let { char ->
            readCharacteristic(char).with { _, data ->
                val psnValue = data.getStringValue(0)
                com.totwoo.totwoo.ToTwooApplication.cacheData?.psn =
                    com.totwoo.totwoo.utils.CommonUtils.hexAsciiToString(psnValue)
            }.enqueue()
        }
    }

    /**
     * 初始化设备
     */
    private fun initializeDevice() {
        requestMtu(247).enqueue()
        // 同步UTC时间
        syncUtcTime()

        // 切换数据模式
        changeDataMode(1)

        // 读取历史数据
        readHistoryData()

        // 检查是否首次连接
        if (!PreferencesUtils.getBoolean(context, BleParams.PAIRED_FIRST_CONNECT, false)) {
            statusListener?.onFirstConnect()
            PreferencesUtils.put(context, BleParams.PAIRED_FIRST_CONNECT, true)
        }
    }

    /**
     * 发送睡眠数据回复 - 复用原有逻辑
     */
    private fun writeReplySleepMessage(byteIndex: ByteArray) {
        val packege = AppPackege().apply {
            commandId = 0x04
            data = AppPackege.DataCell(0x01, byteIndex)
        }
        writeAppPackage(packege)
    }

    /**
     * 发送在一起时间数据回复
     */
    private fun writeReplyTogetherTimeMessage(byteIndex: ByteArray) {
        val packege = AppPackege().apply {
            commandId = 0x04
            data = AppPackege.DataCell(0x03, byteIndex)
        }
        writeAppPackage(packege)
    }

    // ========== 业务方法 - 从BluetoothWrapper迁移 ==========

    /**
     * 睡眠功能
     */
    fun sleep(key: Int) {
        val packege = AppPackege().apply {
            commandId = 0x01
            data = AppPackege.DataCell(0x05, byteArrayOf(key.toByte()))
        }
        writeAppPackage(packege)
    }

    /**
     * 写入睡眠时间
     */
    fun writeSleepMessage(min: Int) {
        val packege = AppPackege().apply {
            commandId = 0x01
            data = AppPackege.DataCell(0x06, BleUtils.getBytes(min))
        }
        writeAppPackage(packege)
    }

    /**
     * 获取MAC地址
     */
    fun getMacAddress() {
        val packege = AppPackege().apply {
            commandId = 0x03
            data = AppPackege.DataCell(0x02, null)
        }
        writeAppPackage(packege)
    }

    /**
     * 设置MAC地址
     */
    fun setMacAddress(address: String) {
        val packege = AppPackege().apply {
            commandId = 0x01
            data = AppPackege.DataCell(0x07, address.toByteArray())
        }
        writeAppPackage(packege)
    }

    /**
     * 获取在一起时间
     */
    fun getTogetherTime() {
        val packege = AppPackege().apply {
            commandId = 0x03
            data = AppPackege.DataCell(0x03, null)
        }
        writeAppPackage(packege)
    }

    /**
     * 写入在一起时间
     */
    fun writeTogetherTimeMessage(min: Int) {
        val packege = AppPackege().apply {
            commandId = 0x01
            data = AppPackege.DataCell(0x08, BleUtils.getBytes(min))
        }
        writeAppPackage(packege)
    }

    /**
     * 保存或者更新本地健步数据数据, 并发送广播通知健步数据更新
     * 复用BluetoothWrapper中的实现逻辑
     */
    private fun saveLocalStepData(step: com.totwoo.totwoo.bean.Step, needBroadcast: Boolean) {
        // 如果是空数据, 或者异常数据, 直接跳过
        if (step.dateTime <= 0) {
            return
        }
        if (step.steps <= 0 && !needBroadcast) {
            return
        }

        try {
            // 保存到数据库
            com.totwoo.totwoo.utils.DbHelper.getDbUtils().saveOrUpdate(step)
        } catch (e: Exception) {
            LogUtils.e(TAG, "保存健步数据失败: ${e.message}")
        }

        if (needBroadcast) {
            // 发送EventBus事件
            org.greenrobot.eventbus.EventBus.getDefault().post(step)
            if (step.isNofify) {
                statusListener?.onNeedNotificationStep()
            }
        }
    }

    /**
     * 清理特征值引用
     */
    private fun clearCharacteristics() {
        batteryLevelChar = null
        writeChar = null
        notifyChar = null
        firmwareRevisionChar = null
        chargeChar = null
        controlChar = null
        controlNotifyChar = null
        modelNoChar = null
        hardwareChar = null
        psnChar = null
    }

    /**
     * 处理通知数据 - 复用原有解析逻辑
     */
    private fun handleNotifyData(data: ByteArray) {
        LogUtils.d(TAG, "收到通知数据: ${BleUtils.bytesToHexString(data)}")

        // 复用原有的数据解析逻辑
        parseNotifyData(data)
    }

    /**
     * 处理控制通知数据
     */
    private fun handleControlNotifyData(
        data: ByteArray,
        controlNotifyChar: BluetoothGattCharacteristic
    ) {
        LogUtils.d(TAG, "收到控制通知数据: ${BleUtils.bytesToHexString(data)}")

        val tempChar = BluetoothGattCharacteristic(
            this.controlNotifyChar?.uuid ?: UUID.fromString("0000fd09-0000-1000-8000-00805f9b34fb"),
            BluetoothGattCharacteristic.PROPERTY_NOTIFY,
            0
        )
        tempChar.value = data


    }

    /**
     * 解析通知数据 - 复用BluetoothWrapper中的parseSysData逻辑
     */
    private fun parseNotifyData(rawValue: ByteArray) {

        if (rawValue.isEmpty()) return

        val headByte = rawValue[0]

        when {
            // 睡眠数据
            headByte == 0xC1.toByte() -> {
                BleUtils.parseSleepData(rawValue)
                // 发送睡眠数据回复 - 复用原有逻辑
                if (rawValue.size >= 3) {
                    val byteIndex = byteArrayOf(rawValue[1], rawValue[2])
                    writeReplySleepMessage(byteIndex)
                }
            }
            // 在一起时间数据
            headByte == 0xC3.toByte() -> {
                BleUtils.parseTogetherTimeData(rawValue)
                // 发送在一起时间数据回复
                if (rawValue.size >= 3) {
                    val byteIndex = byteArrayOf(rawValue[1], rawValue[2])
                    writeReplyTogetherTimeMessage(byteIndex)
                }
            }
            // 应用层数据包
            else -> {
                try {
                    val appPackage = BleUtils.parseTranData(rawValue)
                    handleAppPackage(appPackage)
                } catch (e: Exception) {
                    LogUtils.e("解析应用数据包失败", e)
                }
            }
        }
    }

    /**
     * 处理应用数据包 - 复用原有逻辑
     */
    private fun handleAppPackage(appPackage: AppPackege?) {
        appPackage ?: return

        when (appPackage.commandId.toInt()) {
            1 -> {
                // 实时健步数据 - 完整处理
                LogUtils.d(TAG, "收到实时健步数据")
                val firstValue = appPackage.multi_data?.get(0)?.key_value
                if (firstValue != null && firstValue.size >= 9) {
                    // 解析健步数据
                    val stepCount = BleUtils.getInt(firstValue.copyOf(3))
                    val step = com.totwoo.totwoo.bean.Step(stepCount).apply {
                        isNofify = firstValue[3] == 1.toByte()
                        // 设置日期时间 - 使用正确的方法名
                        setDateTime1(dateTime + 86400000L)
                    }

                    // 保存本地健步数据
                    saveLocalStepData(step, true)

                    // 久坐提醒相关
                    val isSed = firstValue[8] == 1.toByte()
                    val nowSed = BleUtils.getInt(firstValue.copyOfRange(4, 6))

                    if (isSed) {
                        statusListener?.onNeedNotificationSed()
                    }

                    // 发送久坐数据事件
                    val bean = com.totwoo.totwoo.bean.holderBean.HomeSedentaryBean(
                        nowSed.toDouble(), isSed
                    )
                    org.greenrobot.eventbus.EventBus.getDefault().post(bean)
                }
            }

            2 -> {
                // 收到totwoo
                LogUtils.d(TAG, "收到totwoo触摸")

                // 发送回复数据包
                if (!appPackage.isReply) {
                    val replyData = BleUtils.packetReplyData(appPackage.index)
                    writeChar?.let { char ->
                        writeCharacteristic(char, replyData).split().enqueue()
                    }
                } else {
                    return
                }

                // 处理不同类型的触摸事件
                val firstData = appPackage.multi_data?.get(0)
                when (firstData?.key?.toInt()) {
                    0x04 -> {
                        if (BleParams.isMWJewlery()) {
                            statusListener?.onJewTouched()
                            com.etone.framework.event.EventBus.onPostReceived(
                                com.totwoo.totwoo.S.E.E_MUSIC_PLAY_STOP_BY_JEW, null
                            )
                        }
                    }

                    0x05 -> {
                        statusListener?.onJewTripleClicked()
                    }

                    else -> {
                        statusListener?.onJewClicked()
                    }
                }
            }

            3 -> {
                // 同步历史数据
                LogUtils.d(TAG, "收到历史数据")

                // 处理历史数据 - 从BluetoothWrapper移植过来的逻辑
                appPackage.multi_data?.let { multiData ->
                    for (cell in multiData) {
                        when (cell.key.toInt() and 0xff) {
                            0x01 -> {
                                // 处理步数数据
                                val keyValue = cell.key_value
                                if (keyValue != null) {
                                    val count = keyValue.size / 8
                                    for (i in 0 until count) {
                                        val celldata = keyValue.copyOfRange(i * 8, i * 8 + 8)

                                        // 时间戳转换：UTC时间转换并减去1小时
                                        val timestamp = (BleUtils.changeToUTC(
                                            BleUtils.getInt(celldata.copyOfRange(0, 4), true)
                                        ) - 3600) * 1000L

                                        // 步数数据
                                        val stepCount = BleUtils.getInt(celldata.copyOfRange(4, 8), true)

                                        val totalStep = com.totwoo.totwoo.bean.Step(timestamp, stepCount)
                                        totalStep.setDateTime1(totalStep.dateTime)
                                        saveLocalStepData(totalStep, false)
                                    }
                                }
                            }
                            0x03 -> {
                                // 处理久坐时间数据
                                val keyValue = cell.key_value
                                LogUtils.e(TAG, "久坐时间 cell.key_value: ${keyValue?.contentToString()}")
                                if (keyValue != null) {
                                    val count = keyValue.size / 8
                                    for (i in 0 until count) {
                                        val celldata = keyValue.copyOfRange(i * 8, i * 8 + 8)

                                        // 时间戳转换：UTC时间转换并减去1小时
                                        val timestamp = (BleUtils.changeToUTC(
                                            BleUtils.getInt(celldata.copyOfRange(0, 4), true)
                                        ) - 3600) * 1000L

                                        // 久坐时间数据
                                        val sedentaryTime = BleUtils.getInt(celldata.copyOfRange(4, 8), true)

                                        val totalStep = com.totwoo.totwoo.bean.Step(timestamp, sedentaryTime)
                                        totalStep.setDateTime1(totalStep.dateTime)
                                        saveLocalStepData(totalStep, false)
                                    }
                                }
                            }
                        }
                    }
                }
            }

            4 -> {
                // 久坐时间
            }

            5 -> {
                // 固件返回信息 - 直接解析皓月配置并发送到Flow
                val firstValue = appPackage.multi_data?.get(0)?.key_value
                val key = appPackage.multi_data?.get(0)?.key?.toInt() ?: -1

                // 读取皓月配置 - 直接解析业务数据
                if (key == 0x04 && firstValue != null && firstValue.size == 6) {
                    val jewSettings = JewSettings(
                        firstValue[4] == 0.toByte(), // 通知灯光开关 1是关 0是开
                        BleUtils.getInt(firstValue.copyOfRange(5, 6), true),
                        BleUtils.bytesToHexString(firstValue.copyOfRange(1, 4))
                    )

                    // 存本地
                    PreferencesUtils.put(
                        appContext, "jewelry_glitter_enabled", jewSettings.isGlitterEnabled
                    )

                    // 发送到Flow
                    val emitResult = _jewSettingsFlow.tryEmit(jewSettings)
                }
            }

            else -> {
                LogUtils.w(TAG, "未知命令ID: ${appPackage.commandId}")
            }
        }
    }

    /**
     * 解绑, 绑定, OTA 操作状态的情况
     */
    private fun parseControlNotifyData(characteristic: BluetoothGattCharacteristic) {
        val rawValue = characteristic.value ?: return

        if (rawValue.isEmpty()) return

        when (rawValue[0].toInt() and 0xFF) {
            0x01 -> {
                // OTA模式确认
                LogUtils.d(TAG, "设备已进入OTA模式")
                statusListener?.onOTAWriteSuccessed()
            }

            0xB0 -> {
                // IMEI响应
                if (rawValue.size > 1) {
                    val imei = String(rawValue, 1, rawValue.size - 1)
                    LogUtils.d(TAG, "收到IMEI: $imei")
                    PreferencesUtils.put(context, BleParams.SAFE_JEWLERY_IMEI, imei)
                    statusListener?.onImeiGetSuccessed()
                }
            }

            else -> {
                LogUtils.w(TAG, "未知控制响应: ${rawValue[0]}")
            }
        }
    }
}
