package com.totwoo.totwoo.ble;

import static android.bluetooth.BluetoothProfile.STATE_CONNECTING;
import static com.totwoo.totwoo.ToTwooApplication.baseContext;
import static com.totwoo.totwoo.utils.CommonArgs.COLOR_VALUE;
import static com.totwoo.totwoo.utils.CommonArgs.MUSIC_PART_VALUE;
import static com.totwoo.totwoo.utils.CommonArgs.MUSIC_VALUE;

import android.app.Activity;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationManagerCompat;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.Utils;
import com.totwoo.library.exception.DbException;
import com.totwoo.library.net.FileDownloadCallback;
import com.totwoo.library.net.HttpRequest;
import com.totwoo.library.util.Apputils;
import com.totwoo.library.util.LogUtils;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.S;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.activity.JewelryOTAActivity;
import com.totwoo.totwoo.activity.StepCounterActivity;
import com.totwoo.totwoo.activity.homeActivities.HomeActivityControl;
import com.totwoo.totwoo.activity.homeActivities.SecurityHomeActivity;
import com.totwoo.totwoo.activity.security.SafeJewSettingActivity;
import com.totwoo.totwoo.bean.JewSettings;
import com.totwoo.totwoo.bean.JewUpdate;
import com.totwoo.totwoo.bean.JewelryNotifyModel;
import com.totwoo.totwoo.bean.LocalJewelryInfo;
import com.totwoo.totwoo.bean.NotifyDataModel;
import com.totwoo.totwoo.bean.Sedentary;
import com.totwoo.totwoo.bean.StartOtaParams;
import com.totwoo.totwoo.bean.TouchColorBean;
import com.totwoo.totwoo.bean.holderBean.BrightSwitch;
import com.totwoo.totwoo.bean.holderBean.HttpBaseBean;
import com.totwoo.totwoo.ble.nordic.ScanManager;
import com.totwoo.totwoo.data.DataStatisticsClient;
import com.totwoo.totwoo.data.TotwooLogic;
import com.totwoo.totwoo.keepalive.companion.CompanionDeviceHelper;
import com.totwoo.totwoo.utils.CommonArgs;
import com.totwoo.totwoo.utils.CommonUtils;
import com.totwoo.totwoo.utils.FileUtils;
import com.totwoo.totwoo.utils.HttpHelper;
import com.totwoo.totwoo.utils.LocalJewelryDBHelper;
import com.totwoo.totwoo.utils.NotifyUtil;
import com.totwoo.totwoo.utils.PermissionUtil;
import com.totwoo.totwoo.utils.PreferencesUtils;
import com.totwoo.totwoo.utils.ToastUtils;
import com.totwoo.totwoo.utils.TrackEvent;
import com.umeng.analytics.MobclickAgent;

import org.greenrobot.eventbus.EventBus;

import java.io.File;
import java.util.Calendar;
import java.util.List;

import kotlinx.coroutines.flow.SharedFlow;
import no.nordicsemi.android.support.v18.scanner.ScanResult;
import rx.Observer;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * 蓝牙底层管理类。
 * 业务层不直接处理底层事务。
 */
public class BluetoothManage implements com.totwoo.totwoo.ble.nordic.NordicBleCore.StatusListener {
    private static volatile BluetoothManage instance;
    private boolean isUnPairing;
    private Context mContext;

    // OTA 检查缓存相关常量
    private static final long OTA_CHECK_CACHE_DURATION = 12 * 60 * 60 * 1000L; // 12小时缓存时间
    private static final String PREF_LAST_OTA_CHECK_TIME = "last_ota_check_time";
    private static final String PREF_LAST_OTA_CHECK_RESULT = "last_ota_check_result";

    // Nordic BLE组件
    private com.totwoo.totwoo.ble.nordic.ScanManager scanManager;
    private com.totwoo.totwoo.ble.nordic.NordicBleCore nordicBleCore;

    // OTA监听器
    private BluetoothOTAListener bluetoothOTAListener;
    private WriteSuccessListener writeSuccessListener;
    private ConnectSuccessListener connectSuccessListener;
    private ImeiGetSuccessListener imeiGetSuccessListener;
    private static final String TAG = "BluetoothManage";

    private JewSettingsListener jewSettingsListener;


    public interface JewSettingsListener {
        void onJewSettingsReceived(JewSettings settings);
    }

    public void setJewSettingsListener(JewSettingsListener listener) {
        this.jewSettingsListener = listener;
    }

    /**
     * 获取皓月配置数据流，用于协程和Flow方式处理数据
     */
    public SharedFlow<JewSettings> getJewSettingsFlow() {
        return nordicBleCore.getJewSettingsFlow();
    }

    private BluetoothManage() {
        initBleWrapper();
    }

    public static BluetoothManage getInstance() {
        if (instance == null) {
            synchronized (BluetoothManage.class) {
                instance = new BluetoothManage();
            }
        }
        return instance;
    }



    public void startAddressScanForReconnect (String address) {
        nordicBleCore.startAddressScanForReconnect(address);
    }

    public void initBleWrapper() {
        this.mContext = Utils.getApp();

        // 初始化Nordic BLE组件
        scanManager = ScanManager.INSTANCE;
        nordicBleCore = new com.totwoo.totwoo.ble.nordic.NordicBleCore(mContext);

        // 设置状态监听 - 简化适配器
        nordicBleCore.setStatusListener(this);

        // 初始化CompanionDevice组件
        CompanionDeviceHelper.INSTANCE.init();
    }

    public void blueToothTurnOn(Context context) {
        // 每次蓝牙关闭, 会取消之前的后台扫描任务, 这里重启一下
        startBackgroundScan();

        if (!TextUtils.isEmpty(PreferencesUtils.getString(context, BleParams.PAIRED_JEWELRY_NAME_TAG, ""))) {
            reconnect(false);
        } else {
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_BLUETOOTH_OPEN, null);
        }
    }

    public void blueToothTurnOff() {
        disconnectCurrent();
        stopBackgroundScan();
        // Nordic BLE内置重试机制，无需手动设置重试次数
    }


    /**
     * 强制重新连接首饰, 与 {@link #reconnect(boolean)} 区别在于是否重新做蓝牙的扫描
     *
     * @param fromUser 用户手动触发还是自动重连的逻辑触发; 如果用户触发, 会做权限弹窗, 蓝牙弹窗等
     */
    public void forceReconnect(boolean fromUser) {
        if (!PermissionUtil.hasBluetoothPermissionQuiet()) {
            return;
        }

        String address = PreferencesUtils.getString(mContext, BleParams.PAIRED_BLE_ADRESS_TAG, "");
        if (!TextUtils.isEmpty(address)) {
            if (BluetoothManage.getInstance().isConnecting()) {
                BluetoothManage.getInstance().disconnectCurrent();
            }
            JewInfoSingleton.getInstance().setConnectState(JewInfoSingleton.STATE_RECONNECTING);
            BluetoothManage.getInstance().startAddressScanForReconnect(address);
        }
    }

    /**
     * 重连设备
     *
     * @param fromUser 用户手动触发还是自动重连的逻辑触发; 如果用户触发, 会做权限弹窗, w
     */
    public void reconnect(boolean fromUser) {
        if (!BleParams.isBluetoothJewelry(null) || !PermissionUtil.hasBluetoothPermissionQuiet() || !BleUtils.isBlEEnable(Utils.getApp())) {
            return;
        }

        if (isUnPairing) {
            return;
        }
        // 使用智能重连逻辑：优先直接连接，避免不必要的扫描
        nordicBleCore.reconnectDevice();
    }


    /**
     * 开启后台的蓝牙扫描, 做保处理
     * 现在委托给ScanManager处理
     *
     * @return 是否开启成功
     */
    public void startBackgroundScan() {
        //12以上由配对服务保活，无需后台扫描
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S || Apputils.isScreenOn()) {
            return;
        }

        scanManager.startBackgroundScan(mContext);
    }

    public void stopBackgroundScan() {
        scanManager.stopBackgroundScan(mContext);
    }

    /**
     * 对于外部提供的扫描结果, 直接进行连接
     *
     * @param res
     */
    public void connect(ScanResult res) {
        com.tencent.mars.xlog.Log.d(TAG,"后台扫描触发");
        connect(res.getDevice());
    }

    /**
     * 对于外部提供的扫描结果, 直接进行连接
     *
     * @param device
     */
    public void connect(BluetoothDevice device) {
        nordicBleCore.connectDevice(device);
    }


    public void scanOTA() {
        if (!PermissionUtil.hasBluetoothPermissionQuiet()) {
            return;
        }

        scanManager.startOTAScan(new com.totwoo.totwoo.ble.nordic.ScanManager.ScanCallback() {
            @Override
            public void onOTADeviceFound(android.bluetooth.BluetoothDevice device) {
                // 通知OTA设备找到
                if (bluetoothOTAListener != null) {
                    bluetoothOTAListener.onOTADeviceScanned(device);
                }
            }

            @Override
            public void onScanTimeout() {
                if (bluetoothOTAListener != null) {
                    bluetoothOTAListener.onOTATimeOut();
                }
            }

            @Override
            public void onScanFailed(int errorCode) {
                if (bluetoothOTAListener != null) {
                    bluetoothOTAListener.onOTATimeOut();
                }
            }

            @Override
            public void onDeviceFound(android.bluetooth.BluetoothDevice device, int rssi, no.nordicsemi.android.support.v18.scanner.ScanRecord scanRecord) {
            }

            @Override
            public void onAddressDeviceFound(android.bluetooth.BluetoothDevice device) {
            }
        });
    }

    public void setOTAScanListener(BluetoothOTAListener bluetoothOTAListener) {
        this.bluetoothOTAListener = bluetoothOTAListener;
    }

    public void skipScan() {
        scanManager.skipScan();
    }

    public void setOTAScanned(boolean otaScanned) {
        // OTA扫描状态由ScanManager管理，无需额外设置
    }


    public void readBattery() {
        if (isConnected()) {
            nordicBleCore.readBattery();
        }
    }

    public void readFirmwareVersion() {
        checkConnected();
        nordicBleCore.readFirmwareVersion();
    }


    public boolean getBondedDevices() {
        if (!BleUtils.isBlEEnable(mContext)) {
            return false;
        }
        if (JewInfoSingleton.getInstance().getConnectState() == JewInfoSingleton.STATE_UNPAIRED) {
            return false;
        }
        checkConnected();
        // Nordic BLE使用系统的配对设备管理，无需自定义实现
        return false;
    }

    public void enterOtaMode() {
        checkConnected();
        LogUtils.d("enterOtaMode");
        nordicBleCore.enterOTAMode();
    }

    public void imeiMessage() {
        checkConnected();
        nordicBleCore.sendIMEIRequest();
    }

    //下这三个指令的时候，提前判断连接状态。没有连接就直接跳过了
    private int resetMessageNumber = 0;

    public void resetMessages() {
        resetMessageNumber = 0;
//        resetJew();
        Sedentary sedentary = new Sedentary(false, 60, DateUtil.getStringToDate("HH:mm", "09:00"), DateUtil.getStringToDate("HH:mm", "18:00"), "");
        setSedentaryReminder(sedentary);
        setStepTarget(0);
    }

    /**
     * 设置久坐提醒的时间
     *
     * @param sed
     */
    public void setSedentaryReminder(Sedentary sed) {
        checkConnected();

        AppPackege packege = new AppPackege();
        packege.commandId = 0x01;
        byte[] key_value = new byte[10];
        key_value[0] = getRepeatValue(sed.getRepeatRemind());

        Calendar cal = Calendar.getInstance();

        cal.setTimeInMillis(sed.getStartTime());
        key_value[1] = (byte) cal.get(Calendar.HOUR_OF_DAY);
        key_value[2] = (byte) cal.get(Calendar.MINUTE);

        cal.setTimeInMillis(sed.getStopTime());
        key_value[3] = (byte) cal.get(Calendar.HOUR_OF_DAY);
        key_value[4] = (byte) cal.get(Calendar.MINUTE);
        key_value[9] = (byte) sed.getSitWhenlong();
        packege.data = new AppPackege.DataCell((byte) 0x08, key_value);

        nordicBleCore.writeAppPackage(packege);
    }

    private byte getRepeatValue(String repeatRemind) {
        byte b = 0;
        for (int i = 0; i < 7; i++) {
            b |= (repeatRemind.contains(String.valueOf(i)) ? 1 : 0) << i;
        }
        return b;
    }

    /**
     * 设置久坐提醒的提醒方式
     *
     * @param count 震动次数: 长振为 6 , 短振为 3
     * @param color 颜色值, 如红色 0xff0000 不支持透明度设置
     */
    public void setSedentaryNotify(int count, int color) {
        checkConnected();

        AppPackege packege = new AppPackege();
        packege.commandId = 0x01;
        byte[] key_value = new byte[5];
        key_value[0] = 0x01;
        key_value[1] = (byte) (count);
        key_value[2] = (byte) (color >> 16 & 0xff);
        key_value[3] = (byte) (color >> 8 & 0xff);
        key_value[4] = (byte) (color & 0xff);
        packege.data = new AppPackege.DataCell((byte) 0x09, key_value);

        nordicBleCore.writeAppPackage(packege);
    }

    /**
     * sos 求助信息发送成功的首饰反馈
     *
     * @param success
     */
    public void replySOSResult(boolean success) {

        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;

        packege.data = new AppPackege.DataCell((byte) 0x08, new byte[]{(byte) (success ? 1 : 0)});

        nordicBleCore.writeAppPackage(packege);
    }

    /**
     * 页面停留指令
     *
     * @param inActive 是否停留在指定页面
     */
    public void stayIn(boolean inActive) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            return;
        }
        if (!BleParams.isMWJewlery()) {
            return;
        }
        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;

        packege.data = new AppPackege.DataCell((byte) 0x0a, new byte[]{(byte) (inActive ? 1 : 0)});

        nordicBleCore.writeAppPackage(packege);
    }


    /**
     * 触发来电提醒
     *
     * @param count 小于0 表示关闭提醒, 等于零表示触发提醒, 1~6 表示设置时震动反馈次数
     * @param color 提醒颜色
     */
    public void toggleIncommingCall(int count, int color) {
        checkConnected();

        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;
        byte[] key_value = new byte[5];

        if (count >= 0) {
            key_value[0] = (byte) 1;
            key_value[1] = (byte) count;
            key_value[2] = (byte) (color >> 16 & 0xff);
            key_value[3] = (byte) (color >> 8 & 0xff);
            key_value[4] = (byte) (color & 0xff);
        }

        packege.data = new AppPackege.DataCell((byte) 0x04, key_value);
        nordicBleCore.writeAppPackage(packege);
    }

    /**
     * 将首饰灯光设置恢复初始化默认设置
     */
    public void resetJew() {
        checkConnected();
        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;

        packege.data = new AppPackege.DataCell((byte) 0x05, null);

        nordicBleCore.writeAppPackage(packege);
    }

    /**
     * 设置健步目标达成的提醒方式
     *
     * @param count 震动次数: 长振为 6 , 短振为 3
     * @param color 颜色值, 如红色 0xff0000 不支持透明度设置
     */
    public void setStepTargetNotify(int count, int color) {
        checkConnected();

        AppPackege packege = new AppPackege();
        packege.commandId = 0x01;
        byte[] key_value = new byte[5];
        key_value[0] = 0x02;
        key_value[1] = (byte) (count);
        key_value[2] = (byte) (color >> 16 & 0xff);
        key_value[3] = (byte) (color >> 8 & 0xff);
        key_value[4] = (byte) (color & 0xff);
        packege.data = new AppPackege.DataCell((byte) 0x09, key_value);

        nordicBleCore.writeAppPackage(packege);
    }

    /**
     * 设置健步目标
     *
     * @param target 健步目标数
     */
    public void setStepTarget(int target) {
        checkConnected();

        AppPackege packege = new AppPackege();
        packege.commandId = 0x01;

        packege.data = new AppPackege.DataCell((byte) 0x0a,
                new byte[]{(byte) (target >> 8 & 0xff), (byte) (target & 0xff)});

        nordicBleCore.writeAppPackage(packege);
    }


    /**
     * 固件首饰提示, 闪光, 或震动
     *
     * @param count 震动次数: 长振为 6 , 短振为 3
     * @param color 颜色值, 如红色 0xff0000 不支持透明度设置
     * @return
     */
    public void notifyJewelry(int count, int color) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            return;
        }
        //电池款的首饰只有一种响应方式
        if (BleParams.isButtonBatteryJewelry()) {
            count = 6;
        }
        //如果是跑马灯
//        if (color == 0x010101 || color == 0x020202 || color == 0x030303 || color == 0x040404) {
//            count = 0x80;
//        }

        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;
        byte[] key_value = new byte[4];
        key_value[0] = (byte) (count);
        key_value[1] = (byte) (color >> 16 & 0xff);
        key_value[2] = (byte) (color >> 8 & 0xff);
        key_value[3] = (byte) (color & 0xff);
        packege.data = new AppPackege.DataCell((byte) 0x02, key_value);

        nordicBleCore.writeAppPackage(packege);
    }


    /**
     * 固件首饰提示, 闪光, 或震动
     *
     * @param count 震动次数: 长振为 6 , 短振为 3
     * @param color 颜色值, 如红色 0xff0000 不支持透明度设置
     * @return
     */
    public void saveCustomBQ(byte key, int count, int color) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            return;
        }
        //电池款的首饰只有一种响应方式
        if (BleParams.isButtonBatteryJewelry()) {
            count = 6;
        }
        AppPackege packege = new AppPackege();
        packege.commandId = 0x01;
        byte[] key_value = new byte[4];
        key_value[0] = (byte) (count);
        key_value[1] = (byte) (color >> 16 & 0xff);
        key_value[2] = (byte) (color >> 8 & 0xff);
        key_value[3] = (byte) (color & 0xff);
        packege.data = new AppPackege.DataCell(key, key_value);

        nordicBleCore.writeAppPackage(packege);
    }

    /**
     * 固件摩斯码提示, 闪光, 或震动
     *
     * @param count 震动次数: 长振为 6 , 短振为 3
     * @param color 颜色值, 如红色 0xff0000 不支持透明度设置
     * @return
     */
    public void notifyMorseCode(int count, int color) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            return;
        }
        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;
        byte[] key_value = new byte[4];
        key_value[0] = (byte) (0x04);
        key_value[1] = (byte) (color >> 16 & 0xff);
        key_value[2] = (byte) (color >> 8 & 0xff);
        key_value[3] = (byte) (color & 0xff);
        packege.data = new AppPackege.DataCell((byte) 0x02, key_value);

        nordicBleCore.writeAppPackage(packege);
    }

    private boolean brightModeChanged = false;

    public void changeMusicBrightMode(int musicIndex, int color) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            return;
        }
        changeMusicBrightMode(musicIndex, color, false);
    }

    public void changeMusicBrightMode(int musicIndex, int color, boolean track) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            return;
        }

        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;

        byte[] key_value = new byte[5];
        key_value[0] = (byte) (color < 0 ? 0b00000000 : 0b10000000);

        if (musicIndex == 0) {
            key_value[1] = (byte) (color == 0 ? 0x02 : 0x01);
        } else {
            key_value[1] = (byte) ((9 << 4) + musicIndex);
        }

        if (color <= 0) {
            color = 0xff0000;
        }
        key_value[2] = (byte) (color >> 16 & 0xff);
        key_value[3] = (byte) (color >> 8 & 0xff);
        key_value[4] = (byte) (color & 0xff);
        packege.data = new AppPackege.DataCell((byte) 0x01, key_value);

        if (track) {
            brightModeChanged = true;
        }

        nordicBleCore.writeAppPackage(packege);

    }

    public void changeMusicBrightModePreview(int musicIndex, int color) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            return;
        }

        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;

        byte[] key_value = new byte[5];
        key_value[0] = (byte) (color < 0 ? 0b00000000 : 0b10000000);

        if (musicIndex == 0) {
            key_value[1] = (byte) (color == 0 ? 0x02 : 0x01);
        } else {
            key_value[1] = (byte) ((8 << 4) + musicIndex);
        }

        if (color <= 0) {
            color = 0xff0000;
        }
        key_value[2] = (byte) (color >> 16 & 0xff);
        key_value[3] = (byte) (color >> 8 & 0xff);
        key_value[4] = (byte) (color & 0xff);
        packege.data = new AppPackege.DataCell((byte) 0x01, key_value);

        nordicBleCore.writeAppPackage(packege);
    }

    public void notifyBrightMusicOnce(int musicIndex, int color) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            return;
        }

        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;

        byte[] key_value = new byte[5];
        key_value[0] = (byte) (color < 0 ? 0b00000000 : 0b10000000);
        key_value[1] = (byte) ((0x0a << 4) + musicIndex);
        if (color <= 0) {
            color = 0xff0000;
        }
        key_value[2] = (byte) (color >> 16 & 0xff);
        key_value[3] = (byte) (color >> 8 & 0xff);
        key_value[4] = (byte) (color & 0xff);
        packege.data = new AppPackege.DataCell((byte) 0x01, key_value);

        nordicBleCore.writeAppPackage(packege);
    }

    public void notifyBrightMusic(int musicIndex, int color) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            return;
        }

        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;

        byte[] key_value = new byte[5];
        key_value[0] = (byte) (color < 0 ? 0b00000000 : 0b10000000);
        key_value[1] = (byte) ((0x0b << 4) + musicIndex);
        if (color <= 0) {
            color = 0xff0000;
        }
        key_value[2] = (byte) (color >> 16 & 0xff);
        key_value[3] = (byte) (color >> 8 & 0xff);
        key_value[4] = (byte) (color & 0xff);
        packege.data = new AppPackege.DataCell((byte) 0x01, key_value);

        nordicBleCore.writeAppPackage(packege);
    }

    /**
     * 更改璀璨模式的的状态
     *
     * @param color 闪光的颜色值, 如红色 0xff0000 ( 如果颜色为 0 表示七彩光, -1 表示关闭璀璨)
     */
    public void changeBirghtMode(int color) {
        changeBirghtMode(color, false);
    }

    /**
     * 更改璀璨模式的的状态
     *
     * @param color 闪光的颜色值, 如红色 0xff0000 ( 如果颜色为 0 表示七彩光, -1 表示关闭璀璨)
     */
    public void changeBirghtMode(int color, boolean track) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            return;
        }

        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;

        byte[] key_value = new byte[5];
        key_value[0] = (byte) (color < 0 ? 0b00000000 : 0b10000000);
        key_value[1] = (byte) (color == 0 ? 0x02 : 0x01);

        if (color <= 0) {
            color = 0xff0000;
        }
        key_value[2] = (byte) (color >> 16 & 0xff);
        key_value[3] = (byte) (color >> 8 & 0xff);
        key_value[4] = (byte) (color & 0xff);
        packege.data = new AppPackege.DataCell((byte) 0x01, key_value);

        if (track) {
            brightModeChanged = true;
        }
        nordicBleCore.writeAppPackage(packege);
    }

    /**
     * 更新数据传输模式
     *
     * @param mode 传输模式 1 实时模式, 2 正常模式, 3, 后台模式
     */
    public boolean changeDataMode(int mode) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            return false;
        }
        nordicBleCore.changeDataMode(mode);
        return true;
    }


    /**
     * 休眠
     *
     * @param key
     * @return
     */
    public boolean sleep(int key) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            return false;
        }
        nordicBleCore.sleep(key);
        return true;
    }


    /**
     *
     */
    public void notifyJewelryBq(int bq) {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            return;
        }
        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;
        byte[] key_value = new byte[1];
        key_value[0] = (byte) (bq);

        packege.data = new AppPackege.DataCell((byte) 0x07, key_value);

        nordicBleCore.writeAppPackage(packege);
    }

    /**
     * 收到表情, 触发首饰振闪
     *
     * @param index 原来： 2需要你，6伤心 bq_1006, 3你走, 4 对不起，
     */
    public void receiveFace(int index) {
        checkConnected();

        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;

        packege.data = new AppPackege.DataCell((byte) 0x07, new byte[]{(byte) index});

        nordicBleCore.writeAppPackage(packege);
    }

    /**
     * 收到表情, 触发首饰振闪
     *
     * @param index 我爱你的指令是0x10，需要你、伤心、对不起分别是0x11、0x12、0x13
     */
    public void receiveFaceNew(int index) {
        checkConnected();

        AppPackege packege = new AppPackege();
        packege.commandId = 0x02;

        packege.data = new AppPackege.DataCell((byte) 0x07, new byte[]{(byte) index});

        nordicBleCore.writeAppPackage(packege);
    }


    /**
     * 设置全局震动强度
     *
     * @param intensity
     */
    public void setVibrationIntensity(int intensity) {
        checkConnected();

        AppPackege packege = new AppPackege();
        packege.commandId = 0x01;

        packege.data = new AppPackege.DataCell((byte) 0x0c, new byte[]{(byte) intensity});

        nordicBleCore.writeAppPackage(packege);
    }

    /**
     * 设置安全首饰的闪光和振动
     *
     * @param state
     */
    public void setSafeShakeAndShine(int state) {
        checkConnected();

        AppPackege packege = new AppPackege();
        packege.commandId = 0x01;

        packege.data = new AppPackege.DataCell((byte) 0x0E, new byte[]{(byte) state});

        nordicBleCore.writeAppPackage(packege);
    }

    /**
     * 设置安全首饰电量不足的闪光和振动
     *
     * @param state
     */
    public void setSafeBattery(int state) {
        checkConnected();

        AppPackege packege = new AppPackege();
        packege.commandId = 0x01;

        packege.data = new AppPackege.DataCell((byte) 0x0F, new byte[]{(byte) state});

        nordicBleCore.writeAppPackage(packege);
    }

    public void writeSleepMessage(int min) {
        checkConnected();
        LogUtils.e("sleepTime min = " + min);
        nordicBleCore.writeSleepMessage(min);
    }

    public void getMacAddress() {
        checkConnected();
        nordicBleCore.getMacAddress();
    }

    public void setMacAddress(String address) {
        checkConnected();
        nordicBleCore.setMacAddress(address);
    }

    public void getTogetherTime() {
        checkConnected();
        nordicBleCore.getTogetherTime();
    }

    public void writeTogetherTimeMessage(int min) {
        checkConnected();
        LogUtils.e("togetherTime min = " + min);
        nordicBleCore.writeTogetherTimeMessage(min);
    }

    public void setSkip(boolean setSkip) {
        // 跳过扫描功能由ScanManager管理
        if (setSkip) {
            scanManager.skipScan();
        }
    }

    public void readHistoryData() {
        checkConnected();
        nordicBleCore.readHistoryData();
    }

    private boolean startOTAMode = false;

    public String getOTAName() {
        String jew;
        String type = PreferencesUtils.getString(mContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
        switch (type) {
            case BleParams.JEWELRY_BLE_NAME_MP:
                jew = "WE_BLOOM_PENDANT";
                break;
            case BleParams.JEWELRY_BLE_NAME_MB:
                jew = "WE_BLOOM_BRACELET";
                break;
            case BleParams.JEWELRY_BLE_NAME_DP:
                jew = "WE_BLOD_PENDANT";
                break;
            case BleParams.JEWELRY_BLE_NAME_DB:
                jew = "WE_BLOD_BRACELET";
                break;
            case BleParams.JEWELRY_BLE_NAME_BP:
                jew = "WE_BASKETBALL_PENDANT";
                break;
            case BleParams.JEWELRY_BLE_NAME_BB:
                jew = "WE_BASKETBALL_BRACELET";
                break;
            case BleParams.JEWELRY_BLE_NAME_GP:
                jew = "WE_GOLDLION_PENDANT";
                break;
            case BleParams.JEWELRY_BLE_NAME_GB:
                jew = "WE_GOLDLION_BRACELET";
                break;
            case BleParams.JEWELRY_BLE_NAME_MEMORYP:
                jew = "MEMORY_PENDANT";
                break;
            case BleParams.JEWELRY_BLE_NAME_SL:
                jew = "LOVE_PENDANT";
                break;
            case BleParams.JEWELRY_BLE_NAME_SH:
                jew = "SMART_HEART";
                break;
            case BleParams.JEWELRY_BLE_NAME_MGB:
                jew = "METEOR_GARDEN_BRACELET";
                break;
            case BleParams.JEWELRY_BLE_NAME_WBCB:
                jew = "ROPE_BLOD_BRACELET";
                break;
            case BleParams.JEWELRY_BLE_NAME_DP_NEW:
                jew = "WE_BLOD_PENDANT21";
                break;
            case BleParams.JEWELRY_BLE_NAME_DB_NEW:
                jew = "WE_BLOD_BRACELET22";
                break;
            case BleParams.JEWELRY_BLE_NAME_WBCB_NEW:
                jew = "ROPE_BLOD_BRACELET23";
                break;
            case BleParams.JEWELRY_BLE_NAME_SC:
                jew = "CLOVER";
                break;
            case BleParams.JEWELRY_BLE_NAME_WL:
                jew = "WONDERLAND";
                break;
            case BleParams.JEWELRY_BLE_NAME_MP_NEW:
                jew = "WE_BLOOM_PENDANT_NEW";
                break;
            case BleParams.JEWELRY_BLE_NAME_SA:
                jew = "SAFE_JEWELRY_40";
                break;
            case BleParams.JEWELRY_BLE_NAME_SAL:
                jew = "SAFE_JEWELRY_41";
                break;
            case BleParams.JEWELRY_BLE_NAME_SA2:
                jew = "SAFE_JEWELRY_42";
                break;
            case BleParams.JEWELRY_BLE_NAME_SA3:
                jew = "SAFE_JEWELRY_43";
                break;
//            case BleParams.JEWELRY_BLE_NAME_MWP:
//                jew = "MISS_PENDANT";
//                break;
//            case BleParams.JEWELRY_BLE_NAME_MWE:
//                jew = "MISS_EARRINGS";
//                break;
//            case BleParams.JEWELRY_BLE_NAME_MWR:
//                jew = "MISS_RING";
//                break;
            case BleParams.JEWELRY_BLE_NAME_MEMORY_NP:
                jew = "TIMEMEMORY_P";
                break;
            case BleParams.JEWELRY_BLE_NAME_SDP:
                jew = "DREAMLAND_P";
                break;
            case BleParams.JEWELRY_BLE_NAME_SDB:
                jew = "DREAMLAND_B";
                break;
            case BleParams.JEWELRY_BLE_NAME_GFP:
                jew = "LOVEMOSE_P_WOMEN";
                break;
            case BleParams.JEWELRY_BLE_NAME_GFB:
                jew = "LOVEMOSE_B_WOMEN";
                break;
            case BleParams.JEWELRY_BLE_NAME_GMP:
                jew = "LOVEMOSE_P_MEN";
                break;
            case BleParams.JEWELRY_BLE_NAME_GMB:
                jew = "LOVEMOSE_B_MEN";
                break;
            case BleParams.JEWELRY_BLE_NAME_LPP:
                jew = "LOLLIPOP_P_55";
                break;
            case BleParams.JEWELRY_BLE_NAME_LLF:
                jew = "LOVE_LETTER_WOMAN";
                break;
            case BleParams.JEWELRY_BLE_NAME_LLM:
                jew = "LOVE_LETTER_MAN";
                break;
            default:
                jew = type;
        }
        return jew;
    }

    /**
     * 检查 OTA
     */
    public void checkOTA() {
        checkOTA(false);
    }


    /**
     * 检查 OTA
     *
     * @param isManual 是否手动触发更新
     */
    public void checkOTA(boolean isManual) {
        checkOTA(isManual, false);
    }

    /**
     * 检查 OTA
     *
     * @param isManual    是否手动触发更新
     * @param isDowngrade 是否降级情况
     */
    public void checkOTA(boolean isManual, boolean isDowngrade) {
        LogUtils.d("start jewelry ota check, isManual: " + isManual);
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            if (isManual) {
                ToastUtils.showShort(baseContext, CommonUtils.compantGetString(R.string.error_jewelry_connect));
            }
            return;
        }
        String jew = getOTAName();
        if (TextUtils.isEmpty(jew)) {
            ToastUtils.showShort(baseContext, R.string.data_error);
            return;
        }
        String ver = PreferencesUtils.getString(mContext, BleParams.EXTRA_BLE_DATA_TAG_FIRMWARE_REVISION, "");
        String device_info = PreferencesUtils.getString(mContext, BleParams.TOTWOO_DEVICE_INFO, "");

        LogUtils.d("start jewelry ota check, ver: " + ver + ", device_info: " + device_info + ", name: " + jew);

        if (TextUtils.isEmpty(ver)) {
            nordicBleCore.readFirmwareVersion();

            // 进行一次重试
            mHandler.postDelayed(() -> checkOTA(isManual, isDowngrade), 2000);
            return;
        }

        // 只有自动检查时才应用缓存机制，手动检查始终执行
        if (!isManual && shouldSkipOTACheck()) {
            return;
        }

        String model_number = "";
        String hardware_revision = "";
        String psn = "";
        if (ToTwooApplication.cacheData != null) {
            model_number = ToTwooApplication.cacheData.getModel_number();
            hardware_revision = ToTwooApplication.cacheData.getHardware_revision();
            psn = ToTwooApplication.cacheData.getPsn();
        }

        if (!isDowngrade) {
            HttpHelper.update.checkJewUpdate(model_number, hardware_revision, jew, ver.substring(1), Apputils.getVersionName(mContext), device_info, "android", psn)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Observer<HttpBaseBean<JewUpdate>>() {
                        @Override
                        public void onCompleted() {
                        }

                        @Override
                        public void onError(Throwable e) {
                            e.printStackTrace();
                            if (isManual) {
                                ToastUtils.showLong(baseContext, R.string.error_net);
                            }
                        }

                        @Override
                        public void onNext(HttpBaseBean<JewUpdate> httpBaseBean) {
                            if (httpBaseBean.getErrorCode() == 0) {
                                downloadAndStartOta(httpBaseBean, isManual);
                                // 有更新时缓存结果
                                cacheOTACheckResult(ver, true);
                            } else {
                                // 无更新时也缓存结果（无论手动还是自动）
                                cacheOTACheckResult(ver, false);

                                if (isManual) {
                                    Activity topActivity = ActivityUtils.getTopActivity();
                                    if (topActivity != null) {
                                        ToastUtils.showShort(topActivity, R.string.jewelry_info_ota_no_need);
                                    }
                                }
                            }
                        }
                    });
        } else {

            HttpHelper.commonService.checkLow(model_number, hardware_revision, jew, ver.substring(1), psn)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Observer<HttpBaseBean<JewUpdate>>() {
                        @Override
                        public void onCompleted() {
                        }

                        @Override
                        public void onError(Throwable e) {
                            e.printStackTrace();
                            if (isManual) {
                                ToastUtils.showLong(baseContext, R.string.error_net);
                            }
                        }

                        @Override
                        public void onNext(HttpBaseBean<JewUpdate> httpBaseBean) {
                            if (httpBaseBean.getErrorCode() == 0) {
                                downloadAndStartOta(httpBaseBean, isManual);
                            } else if (isManual) {
                                ToastUtils.showShort(baseContext, R.string.jewelry_info_ota_no_need);
                            }
                        }
                    });
        }

    }

    private void downloadAndStartOta(HttpBaseBean<JewUpdate> httpBaseBean, boolean isManual) {
        JewUpdate ju = httpBaseBean.getData();
        MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FIREWARE_UPDATE_PREPARE);
        downloadOtaFile(ju, isManual);

        String language = Apputils.getSystemLanguage(baseContext);
        switch (language) {
            case "zh":
                S.updateString = ju.getCn_text();
                break;
            case "de":
                S.updateString = ju.getDe_text();
                break;
            case "es":
                S.updateString = ju.getEs_text();
                break;
            case "fr":
                S.updateString = ju.getFr_text();
                break;
            case "it":
                S.updateString = ju.getIt_text();
                break;
            case "ja":
                S.updateString = ju.getJa_text();
                break;
            case "ko":
                S.updateString = ju.getKo_text();
                break;
            case "ru":
                S.updateString = ju.getRu_text();
                break;
            case "en":
            default:
                S.updateString = ju.getEn_text();
                break;
        }
    }

    /**
     * 下载最新固件
     *
     * @param jewUpdate
     */
    private void downloadOtaFile(JewUpdate jewUpdate, boolean isManual) {
        if (jewUpdate == null || TextUtils.isEmpty(jewUpdate.getUrl())) {
            if (isManual) {
                ToastUtils.showLong(baseContext, R.string.error_net);
            }
            return;
        }
        final String target = FileUtils.getDownloadDir() + File.separator
                + ("totwoo_" + jewUpdate.getFirmware()).hashCode() + ".zip";
        LogUtils.i("start download ota file: " + jewUpdate.getFirmware() + ", url: " + jewUpdate.getUrl() + ", target: " + target);
        HttpRequest.download(jewUpdate.getUrl(), new File(target),
                new FileDownloadCallback() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        LogUtils.d("download ota file start!");
                    }

                    @Override
                    public void onProgress(int progress, long networkSpeed) {
                        super.onProgress(progress, networkSpeed);
                        LogUtils.d("download ota file progress: " + progress + ", speed: " + networkSpeed);
                    }

                    @Override
                    public void onDone() {
                        LogUtils.d("download ota file success!");
                        PreferencesUtils.put(mContext, JewelryOTAActivity.BLE_OTA_FILE_PATH_TAG, target);

                        startOTA();
                    }

                    @Override
                    public void onFailure() {
                        LogUtils.e("download ota file error! ");
                        if (isManual) {
                            ToastUtils.showLong(baseContext, R.string.error_net);
                        }
                    }
                });
    }

    private void startOTA() {
        //获取电量、获取服务器数据、fd0a 下发指令、 fd09返回结果。
        int batteryLevel = JewInfoSingleton.getInstance().getBattery();
        if (batteryLevel == 0) {
            readBattery();

            mHandler.postDelayed(this::startOTA, 2000);
            return;
        }

        startOTAActivity(batteryLevel < 20, PreferencesUtils.getString(mContext, JewelryOTAActivity.BLE_OTA_FILE_PATH_TAG, ""));
    }

    public void startOTAActivity(boolean isLowPower, String path) {
        Intent otaIntent = new Intent(ToTwooApplication.baseContext, JewelryOTAActivity.class);
        if (isLowPower) {
            MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.FIREWARE_UPDATE_LOWPOWER);
            otaIntent.putExtra(JewelryOTAActivity.IS_LOW_POWER, true);
        } else {
            if (TextUtils.isEmpty(path)) {
                return;
            }
            otaIntent.putExtra(JewelryOTAActivity.BLE_OTA_FILE_PATH_TAG, path);
        }
        otaIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        LogUtils.e("aab startActivity");
        ToTwooApplication.baseContext.startActivity(otaIntent);
    }

    /**
     * 发送 totwoo
     */
    private TotwooLogic totwooLogic;
    private long lastTotwooSend;
    private long lastLoveSend;


    public void sendTotwoo(boolean isJewelry) {
        sendTotwoo(isJewelry, false);
    }


    public void sendTotwoo(boolean isFromJewelry, boolean loveSend) {
        long now = System.currentTimeMillis();
        if (loveSend) {
            if (now - lastLoveSend < 1000) {
                return;
            }
            lastLoveSend = now;
        } else {
            if (now - lastTotwooSend < 1000) {
                return;
            }
            lastTotwooSend = now;
        }

        // 屏蔽 totwoo 发送
        if (blockStatus && isFromJewelry) {
            return;
        }

        // 1.1.0 需求, 去掉为配对状态, 对于首饰摇晃的处理
        if (TextUtils.isEmpty(ToTwooApplication.owner.getPairedId())) {
            return;
        }

        if (totwooLogic == null) {
            totwooLogic = new TotwooLogic(mContext);
        }
        totwooLogic.setTotwooSendCallBack(new TotwooLogic.TotwooSendCallBack() {
            @Override
            public void onSuccess() {
            }

            @Override
            public void onFailed(String error_msg) {
                // 只有在应用在前台时才显示Toast提示
                if (ToTwooApplication.isForeground) {
                    ToastUtils.showLong(mContext, error_msg);
                }
            }
        });
        if (!BleParams.isMemoryJewelry() && !BleParams.isSecurityJewlery() /*&& !BleParams.isMWJewlery()*/ && !BleParams.isSecurityJewlery()) {
            if (loveSend) {

                totwooLogic.totwooSendCustomBQ(false, CommonUtils.CONTENT_LOVE, isFromJewelry);
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.JEW_PAIR_LOVE_U);
            } else {
                MobclickAgent.onEvent(ToTwooApplication.baseContext, TrackEvent.JEW_PAIR_MISS_U);
                totwooLogic.totwooSendCustomBQ(false, CommonUtils.CONTENT_MISS, isFromJewelry);
            }
        }
    }


    public boolean isConnected() {
        return nordicBleCore.isConnected();
    }


    public boolean isConnecting() {
        return nordicBleCore.getConnectionState()  == STATE_CONNECTING;
    }

    private boolean checkConnected() {
        return isConnected();
    }

    public void connectedStatus() {
        if (JewInfoSingleton.getInstance().getConnectState() != JewInfoSingleton.STATE_CONNECTED) {
            ToastUtils.show(ToTwooApplication.baseContext, CommonUtils.compantGetString(R.string.error_jewelry_connect), Toast.LENGTH_SHORT);
        }
    }

    public boolean isUnPairing() {
        return isUnPairing;
    }

    public synchronized void unPair() {
        isUnPairing = true;
        if (BleParams.isMWJewlery()) {
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_STOP_BY_JEW, null);
        } else {
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_MUSIC_PLAY_STOP, null);
        }


        //已连接的时候再去下重置指令，不然直接清空本地数据
        if (nordicBleCore.isConnected()) {
            // Nordic BLE内置队列管理，无需手动清理队列
//            resetMessages();
//            mHandler.postDelayed(() -> {
            if (isUnPairing) {
                nordicBleCore.disconnectDevice().done(device -> {
                            clearLocalPairedData();
                        }).fail((device, status) -> {
                            clearLocalPairedData();
//                        com.etone.framework.event.EventBus.onPostReceived(S.E.E_JEWERLY_APART_FAIL, null);
                        })
                        .enqueue();
            }
//            }, 2000);
        } else {
            nordicBleCore.disconnectDevice().enqueue();
            clearLocalPairedData();
        }
    }

    public void disconnectCurrent() {
//        if (isConnected()) {
            nordicBleCore
                    .disconnectDevice()
                    .fail((device, status) -> com.etone.framework.event.EventBus.onPostReceived(S.E.E_JEWERLY_APART_FAIL, null))
                    .enqueue();
//        }
    }

    private synchronized void clearLocalPairedData() {
        // Nordic BLE内置队列管理，无需手动清理
        EventBus.getDefault().post(new BrightSwitch(false));

        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.SELECT_VIBRATION_INDEX_TAG);
        PreferencesUtils.remove(ToTwooApplication.baseContext, CommonArgs.LOVE_STATUS_SINGLE);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_FIRST_CONNECT);

        NotifyUtil.clearNotifyRemindSet(ToTwooApplication.baseContext);

        // 解绑首饰, 自动关闭璀璨模式, 此处重置下标识
        PreferencesUtils.remove(ToTwooApplication.baseContext, COLOR_VALUE);
        PreferencesUtils.remove(ToTwooApplication.baseContext, MUSIC_VALUE);
        PreferencesUtils.remove(ToTwooApplication.baseContext, MUSIC_PART_VALUE);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.EXTRA_BLE_DATA_TAG_FIRMWARE_REVISION);
        ToTwooApplication.cacheData.clearJewHardInfo();

        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.EXTRA_BLE_DATA_TAG_SYSTEM_ID);
        PreferencesUtils.remove(ToTwooApplication.baseContext, "isShowError");

        PreferencesUtils.remove(ToTwooApplication.baseContext, NotifyUtil.APP_SWITCH_KEY);
        PreferencesUtils.remove(ToTwooApplication.baseContext, NotifyUtil.APP_VIBRATION_SEC_KEY);
        PreferencesUtils.remove(ToTwooApplication.baseContext, NotifyUtil.APP_FLASH_KEY);

        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.TOTWOO_DEVICE_INFO);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_BLE_ADRESS_TAG);
        PreferencesUtils.remove(ToTwooApplication.baseContext, SecurityHomeActivity.IS_IMEI_SENT);
        PreferencesUtils.remove(ToTwooApplication.baseContext, SafeJewSettingActivity.SAFE_SHAKE_STATE);
        PreferencesUtils.remove(ToTwooApplication.baseContext, SafeJewSettingActivity.SAFE_SHINE_STATE);
        PreferencesUtils.remove(ToTwooApplication.baseContext, SafeJewSettingActivity.SAFE_BATTERY_STATE);

        // 清理CompanionDevice配套设备关联
        clearCompanionDeviceAssociation();

        try {
            if (noneBluetoothJewelry(LocalJewelryDBHelper.getInstance().getAllBeans())) {
                JewInfoSingleton.getInstance().setConnectState(JewInfoSingleton.STATE_UNPAIRED);
                if (ToTwooApplication.mService != null) {
                    ToTwooApplication.mService.stopAllKeepAliveComponents();
                }
            }
        } catch (DbException e) {
            e.printStackTrace();
        } finally {
            isUnPairing = false;
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_JEWERLY_APART, null);
        }
    }

    /**
     * 判断给定的数据集中, 是否不包含蓝牙首饰
     *
     * @param allBeans
     * @return
     */
    private boolean noneBluetoothJewelry(List<LocalJewelryInfo> allBeans) {
        if (allBeans != null && allBeans.size() != 0) {
            for (LocalJewelryInfo bean : allBeans) {
                if (BleParams.isBluetoothJewelry(bean.getName())) {
                    return false;
                }
            }
        }
        return true;
    }

    public void clearAndOut() {
        EventBus.getDefault().post(new BrightSwitch(false));

        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.SELECT_VIBRATION_INDEX_TAG);
        PreferencesUtils.remove(ToTwooApplication.baseContext, CommonArgs.LOVE_STATUS_SINGLE);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_FIRST_CONNECT);

        NotifyUtil.clearNotifyRemindSet(ToTwooApplication.baseContext);

        // 解绑首饰, 自动关闭璀璨模式, 此处重置下标识
        PreferencesUtils.remove(ToTwooApplication.baseContext, COLOR_VALUE);
        PreferencesUtils.remove(ToTwooApplication.baseContext, MUSIC_VALUE);
        PreferencesUtils.remove(ToTwooApplication.baseContext, MUSIC_PART_VALUE);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.EXTRA_BLE_DATA_TAG_FIRMWARE_REVISION);
        ToTwooApplication.cacheData.clearJewHardInfo();

        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.EXTRA_BLE_DATA_TAG_SYSTEM_ID);
        PreferencesUtils.remove(ToTwooApplication.baseContext, "isShowError");

        PreferencesUtils.remove(ToTwooApplication.baseContext, NotifyUtil.APP_SWITCH_KEY);
        PreferencesUtils.remove(ToTwooApplication.baseContext, NotifyUtil.APP_VIBRATION_SEC_KEY);
        PreferencesUtils.remove(ToTwooApplication.baseContext, NotifyUtil.APP_FLASH_KEY);

        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.TOTWOO_DEVICE_INFO);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG);
        PreferencesUtils.remove(ToTwooApplication.baseContext, BleParams.PAIRED_BLE_ADRESS_TAG);
        PreferencesUtils.remove(ToTwooApplication.baseContext, SecurityHomeActivity.IS_IMEI_SENT);
        PreferencesUtils.remove(ToTwooApplication.baseContext, SafeJewSettingActivity.SAFE_SHAKE_STATE);
        PreferencesUtils.remove(ToTwooApplication.baseContext, SafeJewSettingActivity.SAFE_SHINE_STATE);
        PreferencesUtils.remove(ToTwooApplication.baseContext, SafeJewSettingActivity.SAFE_BATTERY_STATE);

        // 清理CompanionDevice配套设备关联
        clearCompanionDeviceAssociation();
        if (ToTwooApplication.mService != null) {
            ToTwooApplication.mService.stopAllKeepAliveComponents();
        }

        com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_JEWERLY_APART, null);
    }

    public void setOTA(boolean isOta) {
        // OTA状态由Nordic BLE管理，无需额外设置
        if (!isOta) {
            reconnect(false);
        }
    }

    public void unBonded(BluetoothDevice device) {
        // 使用系统的解绑方法
        try {
            device.getClass().getMethod("removeBond").invoke(device);
        } catch (Exception e) {
            LogUtils.e("解绑设备失败", e);
        }
    }

    private final Handler mHandler = new Handler();

    @Override
    public void onDisconnected() {
        proxyServiceReconnect();
        DataStatisticsClient.triggerJewDisconnect();
    }

    private void proxyServiceReconnect() {
        if (!isUnPairing && ToTwooApplication.mService != null) {
            reconnect(false);
        }
    }

    @Override
    public void onScanAddressOverTime() {
        proxyServiceReconnect();
    }

    @Override
    public void onConnected() {
//        com.etone.framework.event.EventBus.onPostReceived(S.E.E_UPDATE_JEWERLY_CONNECT, null);
        DataStatisticsClient.triggerJewConnect();
        if (connectSuccessListener != null) {
            connectSuccessListener.onConnectSuccessd();
        }
        PreferencesUtils.remove(mContext, BleParams.PREF_RECHECK);
    }

    @Override
    public void onImeiGetSuccessed() {
        imeiGetSuccessListener.onImeiGetSuccess();
    }

    @Override
    public void onWriteSuccessed() {
        if (brightModeChanged) {
            brightModeChanged = false;
            writeSuccessListener.onWriteSuccessed();
        }
        if (isUnPairing) {
            resetMessageNumber++;
            if (resetMessageNumber >= 2) {

                nordicBleCore.disconnectDevice().done(device -> {
                            clearLocalPairedData();
                        }).fail((device, status) -> com.etone.framework.event.EventBus.onPostReceived(S.E.E_JEWERLY_APART_FAIL, null))
                        .enqueue();
            }
        }
    }

    @Override
    public void onOTAWriteSuccessed() {
        StartOtaParams startOtaParams = new StartOtaParams(false, PreferencesUtils.getString(mContext, JewelryOTAActivity.BLE_OTA_FILE_PATH_TAG, ""), PreferencesUtils.getString(mContext, BleParams.PAIRED_BLE_ADRESS_TAG, ""));
        com.etone.framework.event.EventBus.onPostReceived(S.E.E_START_OTA_ACTIVITY, startOtaParams);
    }

    private boolean blockStatus = false;

    public void setBlockStatus(boolean blockStatus) {
        this.blockStatus = blockStatus;
    }

    @Override
    public void onJewClicked() {
        if (blockStatus) {
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_JEWERLY_CLICK, null);
        } else {
            sendTotwoo(true);
        }
    }

    @Override
    public void onJewTripleClicked() {
        if (blockStatus) {
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_JEWERLY_CLICK, null);
        } else {
            sendTotwoo(true, true);
        }
    }

    @Override
    public void onJewTouched() {

        if (blockStatus) {
            com.etone.framework.event.EventBus.onPostReceived(S.E.E_JEWERLY_TOUCH, null);
        }
    }

    @Override
    public void onNeedNotificationSed() {
        boolean notifySwitch = PreferencesUtils.getBoolean(mContext,
                NotifyUtil.SEDENTARY_SWITCH_KEY, NotifyUtil.DEFAULT_SEDENTARY_STATUS);
        if (notifySwitch && CommonUtils.isLogin()) {
            // 对于部分手机时间不更新的情况, 此处采用先取消原有通知, 在新建的方法
            NotificationManagerCompat.from(mContext).cancel(12456);

            NotifyDataModel model = new NotifyDataModel(mContext);
            model.setUser_NickName(ToTwooApplication.owner.getNickName());
            model.setNotify_type(NotifyDataModel.NOTIFY_TYPE_SEDENTARY, true);
            model.setNotify_Id(12456);
            model.setTargetIntent(new Intent(mContext, HomeActivityControl.getInstance().getTagertClass()).putExtra(CommonArgs.HOME_PUSH_TAB_INDEX, 2).putExtra("message", 2));
            model.setNotify_title(mContext.getString(R.string.sedentary_reminder));
            NotifyDataModel.ShowNotify(mContext, model);
        }
    }

    @Override
    public void onFirstConnect() {
        resetMessages();
    }

    @Override
    public void onNeedNotificationStep() {
        JewelryNotifyModel JewModel = NotifyUtil.getStepNotifyModel(mContext);

        if (JewModel.isNotifySwitch() && CommonUtils.isLogin()) {
            NotifyDataModel model = new NotifyDataModel(mContext);
            model.setUser_NickName(ToTwooApplication.owner
                    .getNickName());
            model.setNotify_type(NotifyDataModel.NOTIFY_TYPE_STEP,
                    true);
            model.setNotify_Id(654321);
            model.setTargetIntent(new Intent(mContext,
                    StepCounterActivity.class));
            model.setNotify_title(mContext
                    .getString(R.string.step_swich_title));
            NotifyDataModel.ShowNotify(mContext, model);
        }
    }

    private void notifyQuantity() {
        NotifyDataModel model = new NotifyDataModel(mContext);
        model.setUser_NickName(ToTwooApplication.owner.getNickName());
        model.setNotify_type(NotifyDataModel.NOTIFY_TYPE_QUANTITY,
                true);
        model.setNotify_Id(NotifyDataModel.NOTIFY_TYPE_QUANTITY);
        // model.setTargetIntent(new Intent(context,
        // HomeActivity.class));
        model.setNotify_title(mContext.getString(R.string.jewelry_quantity));
        model.setTargetIntent(new Intent(mContext, HomeActivityControl.getInstance().getTagertClass()));
        NotifyDataModel.ShowNotify(mContext, model);
    }

    @Override
    public void onBatteryChanged(int level) {
        if (level <= 0 || JewInfoSingleton.getInstance().isCharge()) {
            return;
        }
        if (BleParams.isButtonBatteryJewelry()) {
            return;
        }
        //如果是安全首饰，只提示20%和50%
        if (BleParams.isSecurityJewlery()) {
            if (level <= 5) {
                if (!PreferencesUtils.getBoolean(mContext, BleParams.QUANTITYREMINDER_5, false)) {
                    PreferencesUtils.put(mContext, BleParams.QUANTITYREMINDER_5, true);
                    notifyQuantity();
                }
            } else if (level <= 50) {
                if (!PreferencesUtils.getBoolean(mContext, BleParams.QUANTITYREMINDER_50, false)) {
                    PreferencesUtils.put(mContext, BleParams.QUANTITYREMINDER_50, true);
                    notifyQuantity();
                }
                PreferencesUtils.remove(mContext, BleParams.QUANTITYREMINDER_5);
            } else {
                PreferencesUtils.remove(mContext, BleParams.QUANTITYREMINDER_5);
                PreferencesUtils.remove(mContext, BleParams.QUANTITYREMINDER_50);
            }
        } else {
            if (level <= 5) {
                if (!PreferencesUtils.getBoolean(mContext, BleParams.QUANTITYREMINDER_5, false)) {
                    PreferencesUtils.put(mContext, BleParams.QUANTITYREMINDER_5, true);
                    if (!BleParams.isButtonBatteryJewelry()) {
                        notifyQuantity();
                    }
                }
            } else if (level <= 20) {
                if (!PreferencesUtils.getBoolean(mContext, BleParams.QUANTITYREMINDER_20, false)) {
                    PreferencesUtils.put(mContext, BleParams.QUANTITYREMINDER_20, true);
                    if (!BleParams.isButtonBatteryJewelry()) {
                        notifyQuantity();
                    }
                }
                PreferencesUtils.remove(mContext, BleParams.QUANTITYREMINDER_5);
            } else {
                PreferencesUtils.remove(mContext, BleParams.QUANTITYREMINDER_5);
                PreferencesUtils.remove(mContext, BleParams.QUANTITYREMINDER_20);
            }
        }

//        int nowQuantity = level;
//        boolean quantityReminder20 = PreferencesUtils.getBoolean(
//                mContext, BleParams.QUANTITYREMINDER_20, false);
//        boolean quantityReminder5 = PreferencesUtils.getBoolean(
//                mContext, BleParams.QUANTITYREMINDER_5, false);
//        // 电量小于等于20并且开关打开并且5和20的节点并没有都提醒过
//        if (nowQuantity <= 20 && (!quantityReminder20 || !quantityReminder5)) {
//            // 电量大于5并且发了20节点充电提醒直接返回
//            if (nowQuantity > 5 && quantityReminder20) {
//                return;
//            }
//
//            if (nowQuantity > 5) {
//                quantityReminder20 = true;
//            } else {
//                quantityReminder20 = true;
//                quantityReminder5 = true;
//            }
//
//            // 用于NotifyDataModel根据提醒时的电量判断提醒内容
//            PreferencesUtils.put(mContext, BleParams.LASTQUANTITY, nowQuantity);
//            NotifyDataModel model = new NotifyDataModel(mContext);
//            model.setUser_NickName(ToTwooApplication.owner.getNickName());
//            model.setNotify_type(NotifyDataModel.NOTIFY_TYPE_QUANTITY,
//                    true);
//            model.setNotify_Id(NotifyDataModel.NOTIFY_TYPE_QUANTITY);
//            // model.setTargetIntent(new Intent(context,
//            // HomeActivity.class));
//            model.setNotify_title(mContext.getString(R.string.jewelry_quantity));
//            model.setTargetIntent(new Intent(mContext, HomeActivityControl.getInstance().getTagertClass()).putExtra(CommonArgs.HOME_PUSH_TAB_INDEX, 2).putExtra("message", 3));
//            NotifyDataModel.ShowNotify(mContext, model);
//        } else if (nowQuantity > 20) {
//            NotificationManager manager = (NotificationManager) mContext
//                    .getSystemService(Context.NOTIFICATION_SERVICE);
//            manager.cancel(NotifyDataModel.NOTIFY_TYPE_QUANTITY);
//            quantityReminder20 = false;
//            quantityReminder5 = false;
//        }
//
//        PreferencesUtils.put(mContext, BleParams.QUANTITYREMINDER_20,
//                quantityReminder20);
//        PreferencesUtils.put(mContext, BleParams.QUANTITYREMINDER_5,
//                quantityReminder5);
    }

    //
    private void handlePowLevel(int level) {

    }

    public void setWriteSuccessListener(WriteSuccessListener writeSuccessListener) {
        this.writeSuccessListener = writeSuccessListener;
    }

    public interface WriteSuccessListener {

        void onWriteSuccessed();
    }

    public void setConnectSuccessListener(ConnectSuccessListener connectSuccessListener) {
        this.connectSuccessListener = connectSuccessListener;
    }

    public interface ConnectSuccessListener {

        void onConnectSuccessd();
    }

    public void setImeiGetSuccessListener(ImeiGetSuccessListener imeiGetSuccessListener) {
        this.imeiGetSuccessListener = imeiGetSuccessListener;
    }

    public interface ImeiGetSuccessListener {
        void onImeiGetSuccess();
    }

    /**
     * 内部测试代码, 测试不同首饰的不同 UI
     */
    public void testConnectJewelry(@NonNull String name, @NonNull String address) {
        // 测试连接功能，直接触发连接成功回调
        onConnected();
    }


    /**
     * 读取33固件配置
     */
    public void readJewSettings() {
        checkConnected();
        AppPackege packege = new AppPackege();
        packege.commandId = 0x03;
        packege.isReply = false;
        packege.data = new AppPackege.DataCell((byte) 0x04, null);

        nordicBleCore.writeAppPackage(packege);

    }

    /**
     * 设置自定义触摸颜色
     *
     * @param color 颜色值, 如红色 0xff0000 不支持透明度设置
     */
    public void setTouchColor(int color) {
        checkConnected();
        AppPackege packege = new AppPackege();
        packege.commandId = 0x01;
        packege.isReply = false;

        byte[] key_value = new byte[4];
        key_value[0] = (byte) (3);
        key_value[1] = (byte) (color >> 16 & 0xff);
        key_value[2] = (byte) (color >> 8 & 0xff);
        key_value[3] = (byte) (color & 0xff);

        packege.data = new AppPackege.DataCell((byte) 0x16, key_value);

        nordicBleCore.writeAppPackage(packege);
    }


    //自定义触摸颜预览灯光
    public void setTouchColorPreview(int color) {

        checkConnected();
        AppPackege packege = new AppPackege();
        packege.commandId = 0x06;
        packege.isReply = false;

        TouchColorBean touchColorBean = new TouchColorBean(
                0x0, 0x03, 0x0, 0x03);


        byte[] key_value = new byte[7];
        key_value[0] = (byte) (touchColorBean.getDeviceLight());
        key_value[1] = (byte) (touchColorBean.getLightCount());
        //颜色
        key_value[2] = (byte) (color >> 16 & 0xff);
        key_value[3] = (byte) (color >> 8 & 0xff);
        key_value[4] = (byte) (color & 0xff);
        key_value[5] = (byte) (touchColorBean.getDeviceVibra());
        key_value[6] = (byte) (touchColorBean.getVibraCount());
        packege.data = new AppPackege.DataCell((byte) 0x01, key_value);
        nordicBleCore.writeAppPackage(packege);
    }

    /**
     * 设置首饰通知闪光开关
     *
     * @param enabled 是否启用  灯光开关 0开启 1关闭
     */
    public void setJewelryGlitter(boolean enabled) {
        checkConnected();

        AppPackege packege = new AppPackege();
        packege.commandId = 0x01;
        packege.isReply = false;

        byte[] key_value = new byte[1];
        key_value[0] = (byte) (enabled ? 0 : 1);

        packege.data = new AppPackege.DataCell((byte) 0x15, key_value);

        nordicBleCore.writeAppPackage(packege);

    }

    public interface BluetoothOTAListener {
        void onOTADeviceScanned(BluetoothDevice device);

        void onOTATimeOut();
    }

    /**
     * 检查是否应该跳过 OTA 检查（基于6小时缓存机制）
     * 仅在 Release 包下生效，减少网络请求频率
     */
    private boolean shouldSkipOTACheck() {
        try {
            if (ToTwooApplication.isDebug) {
                return false;
            }

//            String currentVersion = PreferencesUtils.getString(mContext, BleParams.EXTRA_BLE_DATA_TAG_FIRMWARE_REVISION, "");
//            if (TextUtils.isEmpty(currentVersion)) {
//                return false; // 版本信息为空，需要检查
//            }

            // 获取缓存的检查信息
            long lastCheckTime = PreferencesUtils.getLong(mContext, generatedKey(PREF_LAST_OTA_CHECK_TIME), 0);
            boolean lastCheckResult = PreferencesUtils.getBoolean(mContext, generatedKey(PREF_LAST_OTA_CHECK_RESULT), false);
            // 检查时间是否超过6小时
            long currentTime = System.currentTimeMillis();
            long timeDiff = currentTime - lastCheckTime;

            if (timeDiff < OTA_CHECK_CACHE_DURATION) {
                // 6小时内且上次检查无更新，跳过检查
                if (!lastCheckResult) {
                    LogUtils.d("OTA check: within 6 hours and no update available, skip check. Time remaining: " +
                            ((OTA_CHECK_CACHE_DURATION - timeDiff) / 1000 / 60) + " minutes");
                    return true;
                } else {
                    LogUtils.d("OTA check: within 6 hours but update was available, need to check");
                    return false; // 上次有更新，继续检查
                }
            }

            LogUtils.d("OTA check: 6 hours passed, need to check");
            return false; // 超过6小时，需要检查
        } catch (Exception e) {
            LogUtils.e("Error in shouldSkipOTACheck: " + e.getMessage());
            return false; // 出错时不跳过检查
        }
    }

    /**
     * 缓存 OTA 检查结果
     * 仅在 Release 包下缓存，用于减少网络请求频率
     *
     * @param firmwareVersion 当前固件版本
     * @param hasUpdate       是否有更新可用
     */
    private void cacheOTACheckResult(String firmwareVersion, boolean hasUpdate) {
        try {
            long currentTime = System.currentTimeMillis();

            // 保存检查时间戳
            PreferencesUtils.put(mContext, generatedKey(PREF_LAST_OTA_CHECK_TIME), currentTime);

            // 保存检查结果
            PreferencesUtils.put(mContext, generatedKey(PREF_LAST_OTA_CHECK_RESULT), hasUpdate);

            LogUtils.d("OTA check result cached: version=" + firmwareVersion +
                    ", hasUpdate=" + hasUpdate + ", time=" + currentTime);
        } catch (Exception e) {
            LogUtils.e("Error caching OTA check result: " + e.getMessage());
        }
    }

    /**
     * 清除 OTA 检查缓存
     * 在手动检查或特殊情况下调用
     */
    public void clearOTACheckCache() {
        try {
            PreferencesUtils.remove(mContext, generatedKey(PREF_LAST_OTA_CHECK_TIME));
            PreferencesUtils.remove(mContext, generatedKey(PREF_LAST_OTA_CHECK_RESULT));
            LogUtils.d("OTA check cache cleared");
        } catch (Exception e) {
            LogUtils.e("Error clearing OTA check cache: " + e.getMessage());
        }
    }

    public String generatedKey(String key) {
        String string = PreferencesUtils.getString(mContext, BleParams.PAIRED_BLE_ADRESS_TAG, "");
        return key + "_" + string;
    }

    /**
     * 清理CompanionDevice配套设备关联
     * 在设备解绑时调用，确保CompanionDevice状态与传统蓝牙配对状态保持一致
     */
    public void clearCompanionDeviceAssociation() {
        try {
            if (CompanionDeviceHelper.INSTANCE.isCompanionDeviceSupported()) {
                // 停止设备存在监听
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    CompanionDeviceHelper.INSTANCE.stopObservingDevicePresence();
                }

                // 取消配套设备关联
                CompanionDeviceHelper.INSTANCE.disassociateDevice();

                LogUtils.d(TAG, "CompanionDevice配套设备关联已清理");
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "清理CompanionDevice关联失败: " + e.getMessage());
        }
    }


    public void  autoAssociateWithPairedDevice(
             Activity activity,  String deviceMac,
             CompanionDeviceHelper.CompanionDeviceCallback callback
    ){
        CompanionDeviceHelper.INSTANCE.autoAssociateWithPairedDevice(activity,deviceMac,callback);
    }
}
