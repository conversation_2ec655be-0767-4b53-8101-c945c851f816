package com.totwoo.totwoo.bean;

/**
 * <AUTHOR>
 * @des:
 * @date 2024/9/19 15:48
 */

public class PermissionItemBean {

    public PermissionItemBean(int id, String permissionName, String permissionDesc, int permissionState) {
        this.id = id;
        this.permissionName = permissionName;
        this.permissionDesc = permissionDesc;
        this.permissionState = permissionState;
    }

    public int id;
    public String permissionName;
    
    public String permissionDesc;
    
    public int permissionState;


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getPermissionName() {
        return permissionName;
    }

    public void setPermissionName(String permissionName) {
        this.permissionName = permissionName;
    }

    public String getPermissionDesc() {
        return permissionDesc;
    }

    public void setPermissionDesc(String permissionDesc) {
        this.permissionDesc = permissionDesc;
    }

    public int getPermissionState() {
        return permissionState;
    }

    public void setPermissionState(int permissionState) {
        this.permissionState = permissionState;
    }

    public static final int PER_NOTIFICATION = 0 ;
    public static final int  PER_BLE = 1 ;
    public static final int  PER_SAVE_POWER = 2;
    public static final int  PER_IGNORING_BATTERY_OPT = 3;
    public static final int  PER_PAUSE_IDLE_APP = 4;
    public static final int  PER_CLOSE_NO_DISTURB = 5;
    public static final int  PER_BACKGROUND_TRAFFIC_ALLOWEDB = 6;
    public static final int  PER_KEEP_BACKGROUND_RUN=7;
    public static final int  APPLIED_QUICK_FREEZING = 8;
    public static final int  PER_BLANK_PASS = 10;
    public static final int  PER_EXACT_ALARM = 11;

    public static final int  PER_COMPANION_DEVICE = 12;
}
